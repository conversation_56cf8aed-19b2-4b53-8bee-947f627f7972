'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Bell, 
  CheckCircle, 
  MoreVertical, 
  Maximize2,
  Minimize2,
  UserPlus, 
  Clock, 
  Shield, 
  Database, 
  Download,
  AlertCircle,
  Search,
  Filter,
  MarkAsRead,
  ChevronDown,
  Sparkles
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useNotifications } from '@/contexts/NotificationContext';
import { useToast } from '@/contexts/ToastContext';
import { useRouter } from 'next/navigation';
import { api } from '@/utils/api';

// Ícones para cada tipo de notificação
const NotificationIcons = {
  NEW_REGISTRATION: <UserPlus size={16} />,
  APPOINTMENT_COMING: <Clock size={16} />,
  NEW_ACCESS: <Shield size={16} />,
  NEW_BACKUP: <Database size={16} />,
  NEW_EXPORT: <Download size={16} />,
  SYSTEM_ALERT: <AlertCircle size={16} />,
  // Notificações específicas para clientes
  NEW_APPOINTMENT_REQUEST: <Clock size={16} />,
  APPOINTMENT_REQUEST_APPROVED: <CheckCircle size={16} />,
  APPOINTMENT_REQUEST_SUGGESTED: <AlertCircle size={16} />,
  APPOINTMENT_REQUEST_REJECTED: <X size={16} />,
  APPOINTMENT_REQUEST_REJECTED_BY_CLIENT: <X size={16} />,
  APPOINTMENT_CREATED_FROM_REQUEST: <CheckCircle size={16} />,
  APPOINTMENT_CONFIRMED: <CheckCircle size={16} />,
  NEW_APPOINTMENT_SCHEDULED: <Clock size={16} />,
  APPOINTMENT_SCHEDULED_FOR_PROVIDER: <Clock size={16} />,
  APPOINTMENT_SCHEDULED_FOR_PATIENT: <Clock size={16} />,
  DOCUMENT_SHARED: <Download size={16} />
};

// Cores para cada tipo de notificação com design mais profissional
const NotificationColors = {
  NEW_REGISTRATION: {
    icon: 'text-emerald-600 bg-emerald-50 dark:bg-emerald-900/20 dark:text-emerald-400',
    border: 'border-emerald-200 dark:border-emerald-800',
    bg: 'bg-emerald-50/50 dark:bg-emerald-900/10'
  },
  APPOINTMENT_COMING: {
    icon: 'text-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400',
    border: 'border-blue-200 dark:border-blue-800',
    bg: 'bg-blue-50/50 dark:bg-blue-900/10'
  },
  NEW_ACCESS: {
    icon: 'text-purple-600 bg-purple-50 dark:bg-purple-900/20 dark:text-purple-400',
    border: 'border-purple-200 dark:border-purple-800',
    bg: 'bg-purple-50/50 dark:bg-purple-900/10'
  },
  NEW_BACKUP: {
    icon: 'text-indigo-600 bg-indigo-50 dark:bg-indigo-900/20 dark:text-indigo-400',
    border: 'border-indigo-200 dark:border-indigo-800',
    bg: 'bg-indigo-50/50 dark:bg-indigo-900/10'
  },
  NEW_EXPORT: {
    icon: 'text-orange-600 bg-orange-50 dark:bg-orange-900/20 dark:text-orange-400',
    border: 'border-orange-200 dark:border-orange-800',
    bg: 'bg-orange-50/50 dark:bg-orange-900/10'
  },
  SYSTEM_ALERT: {
    icon: 'text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400',
    border: 'border-red-200 dark:border-red-800',
    bg: 'bg-red-50/50 dark:bg-red-900/10'
  },
  // Cores para notificações de solicitações
  NEW_APPOINTMENT_REQUEST: {
    icon: 'text-cyan-600 bg-cyan-50 dark:bg-cyan-900/20 dark:text-cyan-400',
    border: 'border-cyan-200 dark:border-cyan-800',
    bg: 'bg-cyan-50/50 dark:bg-cyan-900/10'
  },
  APPOINTMENT_REQUEST_APPROVED: {
    icon: 'text-green-600 bg-green-50 dark:bg-green-900/20 dark:text-green-400',
    border: 'border-green-200 dark:border-green-800',
    bg: 'bg-green-50/50 dark:bg-green-900/10'
  },
  APPOINTMENT_REQUEST_SUGGESTED: {
    icon: 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20 dark:text-yellow-400',
    border: 'border-yellow-200 dark:border-yellow-800',
    bg: 'bg-yellow-50/50 dark:bg-yellow-900/10'
  },
  APPOINTMENT_REQUEST_REJECTED: {
    icon: 'text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400',
    border: 'border-red-200 dark:border-red-800',
    bg: 'bg-red-50/50 dark:bg-red-900/10'
  },
  APPOINTMENT_REQUEST_REJECTED_BY_CLIENT: {
    icon: 'text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400',
    border: 'border-red-200 dark:border-red-800',
    bg: 'bg-red-50/50 dark:bg-red-900/10'
  },
  APPOINTMENT_CREATED_FROM_REQUEST: {
    icon: 'text-emerald-600 bg-emerald-50 dark:bg-emerald-900/20 dark:text-emerald-400',
    border: 'border-emerald-200 dark:border-emerald-800',
    bg: 'bg-emerald-50/50 dark:bg-emerald-900/10'
  },
  APPOINTMENT_CONFIRMED: {
    icon: 'text-emerald-600 bg-emerald-50 dark:bg-emerald-900/20 dark:text-emerald-400',
    border: 'border-emerald-200 dark:border-emerald-800',
    bg: 'bg-emerald-50/50 dark:bg-emerald-900/10'
  },
  NEW_APPOINTMENT_SCHEDULED: {
    icon: 'text-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400',
    border: 'border-blue-200 dark:border-blue-800',
    bg: 'bg-blue-50/50 dark:bg-blue-900/10'
  },
  APPOINTMENT_SCHEDULED_FOR_PROVIDER: {
    icon: 'text-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400',
    border: 'border-blue-200 dark:border-blue-800',
    bg: 'bg-blue-50/50 dark:bg-blue-900/10'
  },
  APPOINTMENT_SCHEDULED_FOR_PATIENT: {
    icon: 'text-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400',
    border: 'border-blue-200 dark:border-blue-800',
    bg: 'bg-blue-50/50 dark:bg-blue-900/10'
  },
  DOCUMENT_SHARED: {
    icon: 'text-teal-600 bg-teal-50 dark:bg-teal-900/20 dark:text-teal-400',
    border: 'border-teal-200 dark:border-teal-800',
    bg: 'bg-teal-50/50 dark:bg-teal-900/10'
  }
};

// Opções de filtro melhoradas
const FilterOptions = [
  { value: 'all', label: 'Todas as notificações' },
  { value: 'unread', label: 'Não lidas' },
  { value: 'READ', label: 'Lidas' },
  { value: 'NEW_REGISTRATION', label: 'Novos cadastros' },
  { value: 'APPOINTMENT_COMING', label: 'Consultas próximas' },
  { value: 'NEW_ACCESS', label: 'Novos acessos' },
  { value: 'NEW_BACKUP', label: 'Backups' },
  { value: 'NEW_EXPORT', label: 'Exportações' },
  { value: 'SYSTEM_ALERT', label: 'Alertas do sistema' },
  // Filtros para solicitações
  { value: 'NEW_APPOINTMENT_REQUEST', label: 'Novas solicitações' },
  { value: 'APPOINTMENT_REQUEST_APPROVED', label: 'Solicitações aprovadas' },
  { value: 'APPOINTMENT_REQUEST_SUGGESTED', label: 'Sugestões recebidas' },
  { value: 'APPOINTMENT_REQUEST_REJECTED', label: 'Solicitações rejeitadas' },
  { value: 'APPOINTMENT_CONFIRMED', label: 'Agendamentos confirmados' },
  { value: 'DOCUMENT_SHARED', label: 'Documentos compartilhados' }
];

const NotificationPanel = ({ isOpen, onClose }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const panelRef = useRef(null);
  const { user } = useAuth();
  const { toast_success, toast_error } = useToast();
  const router = useRouter();
  const { 
    notifications, 
    isLoading, 
    loadNotifications, 
    markAsRead, 
    markAllAsRead 
  } = useNotifications();

  // Carregar notificações quando o painel abrir
  useEffect(() => {
    if (isOpen) {
      loadNotifications();
    }
  }, [isOpen, loadNotifications]);

  // Fechar painel quando clicar fora
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (panelRef.current && !panelRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const handleMarkAsRead = async (notificationId) => {
    try {
      await markAsRead(notificationId);
    } catch (error) {
      console.error('Erro ao marcar notificação como lida:', error);
      toast_error('Erro ao marcar notificação como lida');
    }
  };

  const handleNotificationClick = async (notification) => {
    try {
      // Marcar como lida se não estiver
      if (!notification.read) {
        await handleMarkAsRead(notification.id);
      }

      // Navegar baseado no tipo e dados da notificação
      const { type, data } = notification;
      
      switch (type) {
        case 'NEW_REGISTRATION':
          if (data?.entityType === 'PERSON' && data?.entityId) {
            router.push(`/dashboard/people/persons/${data.entityId}`);
          } else if (data?.entityType === 'CLIENT' && data?.entityId) {
            router.push(`/dashboard/people/clients/${data.entityId}`);
          } else if (data?.entityType === 'USER' && data?.entityId) {
            router.push(`/dashboard/admin/users`);
          } else if (data?.entityType === 'APPOINTMENT' && data?.entityId) {
            // Para novos agendamentos, abrir modal diretamente no calendário
            router.push(`/dashboard/scheduler/calendar?highlightAppointment=${data.entityId}`);
          } else {
            // Fallback para novos registros
            router.push(`/dashboard/people/persons`);
          }
          break;
          
        case 'APPOINTMENT_COMING':
        case 'NEW_APPOINTMENT_SCHEDULED':
        case 'APPOINTMENT_SCHEDULED_FOR_PROVIDER':
        case 'APPOINTMENT_SCHEDULED_FOR_PATIENT':
          if (data?.schedulingId) {
            // Navegar para o calendário com o agendamento específico destacado
            router.push(`/dashboard/scheduler/calendar?highlightAppointment=${data.schedulingId}`);
          } else {
            // Navegar para o calendário geral
            router.push(`/dashboard/scheduler/calendar`);
          }
          break;

        // Notificações de solicitações de agendamento
        case 'NEW_APPOINTMENT_REQUEST':
          // Para usuários internos - ir para lista de solicitações
          if (data?.requestId) {
            router.push(`/dashboard/scheduler/appointment-requests?request=${data.requestId}`);
          } else {
            router.push('/dashboard/scheduler/appointment-requests');
          }
          break;
          
        case 'APPOINTMENT_REQUEST_APPROVED':
        case 'APPOINTMENT_REQUEST_SUGGESTED':
        case 'APPOINTMENT_REQUEST_REJECTED':
          // Para clientes - ir para suas solicitações com destaque
          if (data?.requestId) {
            router.push(`/dashboard/scheduler/requests?request=${data.requestId}`);
          } else {
            router.push('/dashboard/scheduler/requests');
          }
          break;
          
        case 'APPOINTMENT_REQUEST_REJECTED_BY_CLIENT':
        case 'APPOINTMENT_CREATED_FROM_REQUEST':
          // Para usuários internos - ir para lista de solicitações
          if (data?.requestId) {
            router.push(`/dashboard/scheduler/appointment-requests?request=${data.requestId}`);
          } else {
            router.push('/dashboard/scheduler/appointment-requests');
          }
          break;
          
        case 'APPOINTMENT_CONFIRMED':
          // Agendamento confirmado - ir para agenda com destaque no agendamento
          if (data?.schedulingId) {
            router.push(`/dashboard/scheduler/calendar?highlightAppointment=${data.schedulingId}`);
          } else {
            router.push('/dashboard/scheduler/calendar');
          }
          break;
          
        case 'DOCUMENT_SHARED':
          if (data?.documentId) {
            router.push(`/dashboard/people/documents?document=${data.documentId}`);
          } else {
            router.push(`/dashboard/people/documents`);
          }
          break;
          
        case 'NEW_ACCESS':
          router.push(`/dashboard/admin/logs`);
          break;

        case 'NEW_BACKUP':
          router.push(`/dashboard/admin/backup`);
          break;

        case 'NEW_EXPORT':
          router.push(`/dashboard/admin/logs`);
          break;

        case 'SYSTEM_ALERT':
          router.push(`/dashboard/admin/settings`);
          break;
          
        default:
          // Para tipos não mapeados, ir para dashboard
          console.log(`Tipo de notificação não mapeado: ${type}`, data);
          router.push('/dashboard');
      }
      
      // Fechar painel após navegação
      onClose();
    } catch (error) {
      console.error('Erro ao processar clique na notificação:', error);
      toast_error('Erro ao processar notificação');
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
      toast_success('Todas as notificações foram marcadas como lidas');
    } catch (error) {
      console.error('Erro ao marcar todas como lidas:', error);
      toast_error('Erro ao marcar todas as notificações como lidas');
    }
  };

  const formatTimeAgo = (dateString) => {
    const now = new Date();
    const notificationDate = new Date(dateString);
    const diffInMinutes = Math.floor((now - notificationDate) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'agora';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return `${Math.floor(diffInMinutes / 1440)}d`;
  };

  const getNotificationIcon = (type) => {
    return NotificationIcons[type] || <Bell size={16} />;
  };

  const getNotificationColors = (type) => {
    return NotificationColors[type] || {
      icon: 'text-gray-600 bg-gray-50 dark:bg-gray-800 dark:text-gray-400',
      border: 'border-gray-200 dark:border-gray-700',
      bg: 'bg-gray-50/50 dark:bg-gray-800/10'
    };
  };

  // Filtrar notificações baseado na busca e filtro
  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.message.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterType === 'all' || 
                         (filterType === 'unread' && !notification.read) ||
                         (filterType === 'READ' && notification.read) ||
                         notification.type === filterType;
    
    return matchesSearch && matchesFilter;
  });

  const displayedNotifications = isExpanded ? filteredNotifications : filteredNotifications.slice(0, 5);
  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          ref={panelRef}
          initial={{ opacity: 0, scale: 0.95, y: -10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: -10 }}
          transition={{ duration: 0.2 }}
          className={`absolute top-full right-0 mt-2 z-50 ${
            isExpanded ? 'w-[28rem] h-[36rem] max-w-[90vw] sm:max-w-[28rem]' : 'w-80 max-h-[28rem] max-w-[90vw] sm:max-w-80'
          } bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-2xl shadow-2xl ring-1 ring-gray-900/5 dark:ring-white/10 overflow-hidden flex flex-col`}
        >
          {/* Seta apontando para o botão */}
          <div className="absolute -top-2 right-4 w-4 h-4 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl ring-1 ring-gray-900/5 dark:ring-white/10 transform rotate-45"></div>
          
          {/* Header profissional */}
          <div className="relative bg-gradient-to-r from-cyan-600 via-cyan-500 to-teal-500 text-white p-4 flex-shrink-0">
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/20 via-teal-400/20 to-cyan-500/20"></div>
            <div className="relative">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white/10 rounded-xl backdrop-blur-sm">
                    <Bell size={18} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-white">Notificações</h3>
                    <p className="text-xs text-white/70">
                      {unreadCount > 0 ? `${unreadCount} não lidas` : 'Todas lidas'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className={`p-2 rounded-xl transition-all duration-200 ${
                      showFilters ? 'bg-white/20 text-white' : 'hover:bg-white/10 text-white/80'
                    }`}
                    aria-label="Filtros"
                  >
                    <Filter size={16} />
                  </button>
                  <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="p-2 hover:bg-white/10 rounded-xl transition-all duration-200 text-white/80 hover:text-white"
                    aria-label={isExpanded ? 'Minimizar' : 'Expandir'}
                  >
                    {isExpanded ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
                  </button>
                  <button
                    onClick={onClose}
                    className="p-2 hover:bg-white/10 rounded-xl transition-all duration-200 text-white/80 hover:text-white"
                    aria-label="Fechar"
                  >
                    <X size={16} />
                  </button>
                </div>
              </div>
              
              {/* Ação rápida */}
              {unreadCount > 0 && (
                <div className="mt-3 flex justify-end">
                  <button
                    onClick={handleMarkAllAsRead}
                    className="inline-flex items-center gap-2 px-3 py-1.5 bg-white/10 hover:bg-white/20 rounded-lg text-sm font-medium transition-all duration-200 backdrop-blur-sm"
                  >
                    <CheckCircle size={14} />
                    Marcar todas como lidas
                  </button>
                </div>
              )}

              {/* Filtros expandidos */}
              <AnimatePresence>
                {showFilters && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="mt-4 space-y-3"
                  >
                    {/* Busca */}
                    <div className="relative">
                      <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60" />
                      <input
                        type="text"
                        placeholder="Buscar notificações..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-2.5 text-sm bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 backdrop-blur-sm transition-all duration-200"
                      />
                    </div>

                    {/* Filtro por tipo */}
                    <div className="relative">
                      <select
                        value={filterType}
                        onChange={(e) => setFilterType(e.target.value)}
                        className="w-full px-4 py-2.5 text-sm bg-white/10 border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 backdrop-blur-sm transition-all duration-200 appearance-none cursor-pointer"
                      >
                        {FilterOptions.map(option => (
                          <option key={option.value} value={option.value} className="bg-gray-900 text-white">
                            {option.label}
                          </option>
                        ))}
                      </select>
                      <ChevronDown size={16} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 pointer-events-none" />
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          {/* Conteúdo */}
          <div className="flex-1 overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center h-32">
                <div className="relative">
                  <div className="w-8 h-8 border-2 border-gray-300 dark:border-gray-600 rounded-full animate-spin"></div>
                  <div className="absolute inset-0 w-8 h-8 border-2 border-transparent border-t-cyan-500 rounded-full animate-spin"></div>
                </div>
              </div>
            ) : filteredNotifications.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-40 text-gray-500 dark:text-gray-400">
                <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-full mb-4">
                  <Bell size={24} />
                </div>
                <p className="text-sm font-medium mb-1">
                  {searchTerm || filterType !== 'all' ? 'Nenhuma notificação encontrada' : 'Nenhuma notificação'}
                </p>
                <p className="text-xs text-gray-400">
                  {searchTerm || filterType !== 'all' ? 'Tente ajustar os filtros' : 'Você está em dia!'}
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-100 dark:divide-gray-800">
                {displayedNotifications.map((notification, index) => {
                  const colors = getNotificationColors(notification.type);
                  return (
                    <motion.div
                      key={`notification-${notification.id}-${index}`}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`relative p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-all duration-200 ${
                        !notification.read ? `${colors.bg} border-l-4 ${colors.border}` : ''
                      }`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="flex items-start gap-3">
                        <div className={`p-2.5 rounded-xl ${colors.icon} flex-shrink-0 shadow-sm`}>
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-2">
                            <h4 className={`text-sm font-semibold ${
                              !notification.read 
                                ? 'text-gray-900 dark:text-white' 
                                : 'text-gray-700 dark:text-gray-300'
                            }`}>
                              {notification.title}
                            </h4>
                            <div className="flex items-center gap-2 flex-shrink-0">
                              {!notification.read && (
                                <div className="w-2 h-2 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full shadow-sm"></div>
                              )}
                              <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                                {formatTimeAgo(notification.createdAt)}
                              </span>
                            </div>
                          </div>
                          <p className={`text-xs mt-1 leading-relaxed ${
                            !notification.read 
                              ? 'text-gray-600 dark:text-gray-400' 
                              : 'text-gray-500 dark:text-gray-500'
                          }`}>
                            {notification.message}
                          </p>
                          {notification.priority === 'HIGH' && (
                            <div className="mt-2">
                              <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-400 shadow-sm">
                                <Sparkles size={10} />
                                Alta prioridade
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Footer */}
          {!isExpanded && filteredNotifications.length > 5 && (
            <div className="flex-shrink-0 p-4 border-t border-gray-100 dark:border-gray-800 bg-gray-50/50 dark:bg-gray-800/50 backdrop-blur-sm">
              <button
                onClick={() => setIsExpanded(true)}
                className="w-full text-sm text-gray-700 dark:text-gray-300 hover:text-cyan-600 dark:hover:text-cyan-400 font-medium transition-colors duration-200 flex items-center justify-center gap-2"
              >
                <span>Ver mais {filteredNotifications.length - 5} notificações</span>
                <ChevronDown size={14} />
              </button>
            </div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default NotificationPanel;
