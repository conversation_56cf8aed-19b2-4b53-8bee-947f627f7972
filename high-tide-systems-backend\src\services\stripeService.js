// src/services/stripeService.js
const { stripe, MODULES, BASIC_INCLUDED_MODULES, calculatePrice } = require('../config/stripe');
const {
    PrismaClient,
    SystemModule,
    BillingCycle,
    SubscriptionStatus,
    InvoiceStatus,
    Prisma,
} = require('@prisma/client');
const AuthController = require('../controllers/authController');
const prisma = new PrismaClient();

// Helper method to find a company admin for audit logs
async function findCompanyAdmin(companyId) {
    // Find an admin user for this company to associate with the audit log
    const adminUser = await prisma.user.findFirst({
        where: {
            companyId,
            role: 'COMPANY_ADMIN',
            active: true
        }
    });

    return adminUser;
}

// Helper method to safely create audit logs
async function safelyCreateAuditLog(data) {
    // Only create audit log if we have a valid user ID
    if (data.userId) {
        await prisma.auditLog.create({ data });
    } else {
        // If no userId provided, try to find an admin for the company
        if (data.companyId) {
            const adminUser = await findCompanyAdmin(data.companyId);

            if (adminUser) {
                await prisma.auditLog.create({
                    data: {
                        ...data,
                        userId: adminUser.id
                    }
                });
            } else {
                // If no admin user found, log a warning but continue processing
                console.warn(`No admin user found for company ${data.companyId} to associate with audit log`);
            }
        } else {
            console.warn(`Cannot create audit log without userId or companyId: ${JSON.stringify(data)}`);
        }
    }
}

class StripeService {

    /**
     * Cria ou atualiza um cliente Stripe para uma empresa
     */
    async createOrUpdateCustomer(companyId) {
        try {
            const company = await prisma.company.findUnique({
                where: { id: companyId },
            });

            if (!company) {
                throw new Error(`Empresa não encontrada: ${companyId}`);
            }

            let customer;

            if (company.stripeCustomerId) {
                // Atualiza o cliente existente
                customer = await stripe.customers.update(company.stripeCustomerId, {
                    name: company.legalName || company.name,
                    email: company.contactEmail,
                    metadata: {
                        companyId: company.id,
                        cnpj: company.cnpj,
                    },
                });
            } else {
                // Cria um novo cliente
                customer = await stripe.customers.create({
                    name: company.legalName || company.name,
                    email: company.contactEmail,
                    metadata: {
                        companyId: company.id,
                        cnpj: company.cnpj,
                    },
                });

                // Atualiza a empresa com o ID do cliente Stripe
                await prisma.company.update({
                    where: { id: companyId },
                    data: { stripeCustomerId: customer.id },
                });
            }

            return customer;
        } catch (error) {
            console.error('Erro ao criar/atualizar cliente Stripe:', error);
            throw error;
        }
    }

    /**
     * Cria um produto no Stripe (se ainda não existir)
     * Para cada módulo do sistema, criamos um produto no Stripe
     */
    async createProductIfNotExists(moduleType) {
        try {
            const moduleInfo = MODULES[moduleType];

            if (!moduleInfo) {
                throw new Error(`Módulo não encontrado: ${moduleType}`);
            }

            // Verifica se o produto já existe no Stripe
            const existingProducts = await stripe.products.list({
                limit: 100,
            });

            const existingProduct = existingProducts.data.find(
                (product) => product.metadata.moduleType === moduleType
            );

            if (existingProduct) {
                return existingProduct;
            }

            // Cria um novo produto
            const product = await stripe.products.create({
                name: moduleInfo.name,
                description: moduleInfo.description,
                metadata: {
                    moduleType: moduleType,
                },
                active: true,
            });

            return product;
        } catch (error) {
            console.error('Erro ao criar produto Stripe:', error);
            throw error;
        }
    }

    /**
     * Cria um preço no Stripe para o plano básico
     */
    async createPrice(productId, userCount, billingCycle = BillingCycle.MONTHLY) {
        try {
            // Calcula o preço baseado na quantidade de usuários
            const pricing = calculatePrice(userCount, billingCycle === BillingCycle.YEARLY, false);
            const unitAmount = pricing.finalPrice * 100; // Stripe trabalha em centavos

            const price = await stripe.prices.create({
                product: productId,
                unit_amount: Math.round(unitAmount),
                currency: 'brl',
                recurring: {
                    interval: billingCycle === BillingCycle.YEARLY ? 'year' : 'month',
                },
                metadata: {
                    type: 'basic_plan',
                    userCount: userCount.toString(),
                    billingCycle: billingCycle,
                    discount: pricing.discount.toString(),
                },
            });

            return price;
        } catch (error) {
            console.error('Erro ao criar preço Stripe:', error);
            throw error;
        }
    }

    /**
     * Obtém ou cria um preço para o plano básico
     */
    async getPriceForBasicPlan(userCount, billingCycle = BillingCycle.MONTHLY) {
        try {
            // Lista todos os preços ativos no Stripe
            const prices = await stripe.prices.list({
                active: true,
                limit: 100,
            });

            // Billingcycle para string para comparação com metadata
            const billingCycleStr = billingCycle === BillingCycle.YEARLY ? 'YEARLY' : 'MONTHLY';

            // Busca por um preço existente para a quantidade de usuários e ciclo
            const existingPrice = prices.data.find(
                (price) =>
                    price.metadata.type === 'basic_plan' &&
                    price.metadata.userCount === userCount.toString() &&
                    price.metadata.billingCycle === billingCycleStr
            );

            if (existingPrice) {
                return existingPrice;
            }

            // Se não encontrar, cria um novo produto e preço
            const product = await this.createProductIfNotExists('BASIC_PLAN');
            const newPrice = await this.createPrice(product.id, userCount, billingCycle);

            return newPrice;
        } catch (error) {
            console.error('Erro ao obter preço para plano básico:', error);
            throw error;
        }
    }

    /**
     * Cria uma sessão de checkout para uma nova assinatura
     */
    async createCheckoutSession(companyId, billingCycleInput = 'monthly', userLimit = 5, couponCode = null, affiliateCode = null, sessionId = null, upfront = false) {
        try {
            // Converte string para enum
            const billingCycle = billingCycleInput.toUpperCase() === 'YEARLY'
                ? BillingCycle.YEARLY
                : BillingCycle.MONTHLY;

            let company;
            let customerEmail;
            let customerName;

            if (sessionId) {
                // Durante o processo de signup, buscar dados da sessão
                const session = await prisma.signupSession.findUnique({
                    where: { id: sessionId }
                });

                if (!session) {
                    throw new Error(`Sessão de signup não encontrada: ${sessionId}`);
                }

                // Usar dados da sessão para o checkout
                customerEmail = session.email;
                customerName = session.fullName;
                company = null; // Empresa será criada após o pagamento
            } else {
                // Para usuários já autenticados
                company = await prisma.company.findUnique({
                    where: { id: companyId },
                    include: {
                        subscription: true,
                    },
                });

                if (!company) {
                    throw new Error(`Empresa não encontrada: ${companyId}`);
                }

                customerEmail = company.email || '';
                customerName = company.name;
            }

            // Verifica se já existe uma assinatura ativa (apenas para empresas existentes)
            if (company && company.subscription && company.subscription.active) {
                // Permitir upgrade se a assinatura atual é trial
                if (company.subscription.status === 'TRIAL') {
                    console.log(`[STRIPE] Permitindo upgrade de assinatura trial para empresa ${companyId}`);
                } else {
                    throw new Error('A empresa já possui uma assinatura ativa');
                }
            }

            // Cria ou obtém o cliente Stripe
            let customer;
            if (company && company.stripeCustomerId) {
                customer = { id: company.stripeCustomerId };
            } else if (sessionId) {
                // Durante o signup, criar cliente temporário
                customer = await stripe.customers.create({
                    email: customerEmail,
                    name: customerName,
                    metadata: {
                        sessionId: sessionId,
                        signupType: 'paid'
                    }
                });
            } else {
                customer = await this.createOrUpdateCustomer(companyId);
            }

            // Calcula o preço baseado na quantidade de usuários
            const pricing = calculatePrice(userLimit, billingCycle === BillingCycle.YEARLY, upfront);

            console.log(`[STRIPE] Criando checkout para ${userLimit} usuários: R$ ${pricing.finalPrice.toFixed(2)}`);
            console.log(`[STRIPE] Métodos de pagamento disponíveis: Cartão de Crédito, PIX`);
            if (billingCycle === BillingCycle.YEARLY) {
                console.log(`[STRIPE] Plano anual aplicado - Desconto: ${pricing.annualDiscount}%`);
                if (upfront) {
                    console.log(`[STRIPE] Pagamento à vista aplicado - Desconto extra: ${pricing.upfrontDiscount}%`);
                }
            }
            if (couponCode) {
                console.log(`[STRIPE] Cupom aplicado: ${couponCode} - Duração: ${billingCycle === BillingCycle.YEARLY ? '12 meses' : '1 mês'}`);
            }

            // Cria um produto único para o plano básico
            const product = await stripe.products.create({
                name: `High Tide Systems - ${userLimit} usuário${userLimit > 1 ? 's' : ''}`,
                description: `Plano básico com acesso completo para ${userLimit} usuário${userLimit > 1 ? 's' : ''}`,
                metadata: {
                    includedModules: BASIC_INCLUDED_MODULES.join(','),
                    userLimit: userLimit.toString(),
                    discount: pricing.discount.toString()
                }
            });

            // Cria um preço para o plano básico
            const price = await stripe.prices.create({
                product: product.id,
                unit_amount: Math.round(pricing.finalPrice * 100), // Em centavos
                currency: 'brl',
                recurring: {
                    interval: billingCycle === BillingCycle.YEARLY ? 'year' : 'month',
                },
                metadata: {
                    type: 'basic_plan',
                    billingCycle: billingCycle,
                    userCount: userLimit.toString(),
                    discount: pricing.discount.toString(),
                    basePrice: pricing.totalWithoutDiscount.toString(),
                },
            });

            // Se houver cupom, buscar e criar no Stripe se necessário
            let stripeCouponId = null;
            if (couponCode) {
                const coupon = await prisma.coupon.findUnique({ where: { code: couponCode.toUpperCase() } });
                if (!coupon || !coupon.active) throw new Error('Cupom inválido');
                if (coupon.expiresAt && coupon.expiresAt < new Date()) throw new Error('Cupom expirado');
                if (coupon.maxRedemptions) {
                  const count = await prisma.couponRedemption.count({ where: { couponId: coupon.id } });
                  if (count >= coupon.maxRedemptions) throw new Error('Cupom esgotado');
                }
                // Verifica se já existe no Stripe
                const existing = await stripe.coupons.list({ limit: 100 });
                let found = existing.data.find(c => c.name === coupon.code);
                if (!found) {
                  // Cria cupom no Stripe
                  found = await stripe.coupons.create({
                    name: coupon.code,
                    percent_off: coupon.type === 'PERCENT' ? coupon.value : undefined,
                    amount_off: coupon.type === 'VALUE' ? Math.round(coupon.value * 100) : undefined,
                    currency: coupon.type === 'VALUE' ? 'brl' : undefined,
                    duration: 'repeating',
                    duration_in_months: billingCycle === BillingCycle.YEARLY ? 12 : 1
                  });
                }
                stripeCouponId = found.id;
            }

            // Cria a sessão de checkout
            const session = await stripe.checkout.sessions.create({
                payment_method_types: ['card'], // PIX commented out for test environment
                customer: customer.id,
                line_items: [{
                    price: price.id,
                    quantity: 1,
                }],
                mode: 'subscription',
                success_url: `${process.env.FRONTEND_URL}/subscription/success?session_id={CHECKOUT_SESSION_ID}${sessionId ? `&sessionId=${sessionId}` : ''}`,
                cancel_url: `${process.env.FRONTEND_URL}/subscription/cancel`,
                payment_method_configuration: process.env.STRIPE_PAYMENT_METHOD_CONFIG_ID || undefined,
                automatic_tax: {
                    enabled: false,
                },
                billing_address_collection: 'required',
                locale: 'pt-BR',
                currency: 'brl',
                expires_at: Math.floor(Date.now() / 1000) + (30 * 60), // 30 minutos para PIX
                subscription_data: {
                    metadata: {
                        setup_future_usage: 'off_session',
                    },
                },
                metadata: {
                    companyId: companyId || '',
                    billingCycle: billingCycle,
                    userLimit: userLimit.toString(),
                    finalPrice: pricing.finalPrice.toString(),
                    includedModules: BASIC_INCLUDED_MODULES.join(','),
                    couponCode: couponCode || '',
                    affiliateCode: affiliateCode || '',
                    sessionId: sessionId || '',
                    upfront: upfront.toString(),
                    annualDiscount: pricing.annualDiscount.toString(),
                    upfrontDiscount: upfront ? pricing.upfrontDiscount.toString() : '0'
                },
                discounts: stripeCouponId ? [{ coupon: stripeCouponId }] : undefined
            });

            return session;
        } catch (error) {
            console.error('Erro ao criar sessão de checkout:', error);
            throw error;
        }
    }

    /**
     * Processa o webhook de evento de checkout.session.completed
     */
    async handleCheckoutCompleted(event) {
        try {
            const session = event.data.object;
            const customerId = session.customer;
            const subscriptionId = session.subscription;
            const metadata = session.metadata || {};

            // Extract company ID and sessionId from metadata
            const companyId = metadata.companyId;
            const sessionId = metadata.sessionId;

            // Se há sessionId, é um signup via pagamento - criar conta
            if (sessionId) {
                console.log(`[STRIPE] Pagamento de signup detectado - sessionId: ${sessionId}`);
                
                // Buscar dados da sessão de signup
                const signupSession = await prisma.signupSession.findUnique({
                    where: { id: sessionId }
                });

                if (!signupSession || signupSession.used) {
                    console.error(`[STRIPE] Sessão de signup não encontrada ou já utilizada: ${sessionId}`);
                    return;
                }

                // Finalizar signup criando empresa e usuário
                try {
                    await AuthController.finalizeSignupFromWebhook(sessionId, customerId, subscriptionId, stripeSubscription, metadata);
                    console.log(`[STRIPE] Conta criada com sucesso via webhook para sessão: ${sessionId}`);
                    return;
                } catch (error) {
                    console.error(`[STRIPE] Erro ao criar conta via webhook:`, error);
                    return;
                }
            }

            if (!companyId) {
                console.error('Company ID not found in session metadata');
                return;
            }


            // Check if this is a signup flow (no companyId but has sessionId)
            if (!companyId && metadata.sessionId) {
                console.log(`[STRIPE] Processing signup checkout for session: ${metadata.sessionId}`);
                
                // Get signup session data
                const signupSession = await prisma.signupSession.findUnique({
                    where: { id: metadata.sessionId }
                });

                if (!signupSession) {
                    console.error(`Signup session not found: ${metadata.sessionId}`);
                    return;
                }

                const sessionData = signupSession.sessionData;
                
                // Create company from signup data
                company = await prisma.company.create({
                    data: {
                        name: sessionData.companyName,
                        tradingName: sessionData.companyTradingName,
                        cnpj: sessionData.companyCnpj,
                        phone: sessionData.companyPhone,
                        address: sessionData.companyAddress,
                        city: sessionData.companyCity,
                        state: sessionData.companyState,
                        postalCode: sessionData.companyPostalCode,
                        stripeCustomerId: customerId,
                        contactEmail: sessionData.email,
                        active: true,
                        isTrial: false
                    }
                });

                // Create user for the company
                await prisma.user.create({
                    data: {
                        login: sessionData.login,
                        email: sessionData.email,
                        fullName: sessionData.fullName,
                        password: sessionData.password,
                        phone: sessionData.phone,
                        address: sessionData.address,
                        birthDate: new Date(sessionData.birthDate),
                        cpf: sessionData.cpf,
                        role: 'COMPANY_ADMIN',
                        companyId: company.id,
                        active: true,
                        isFirstLogin: true
                    }
                });

                // Mark signup session as used
                await prisma.signupSession.update({
                    where: { id: metadata.sessionId },
                    data: { used: true }
                });

                companyId = company.id;
                console.log(`[STRIPE] Created company: ${company.name} (${companyId})`);
            }
            // Standard flow - existing company
            else if (companyId) {
                company = await prisma.company.findUnique({
                    where: { id: companyId },
                });

                if (!company) {
                    console.error(`Company not found with ID: ${companyId}`);
                    return;
                }
            }
            // No companyId and no sessionId - invalid
            else {
                console.error('Neither company ID nor session ID found in metadata');
                return;
            }

            // Get subscription details from Stripe
            const stripeSubscription = await stripe.subscriptions.retrieve(subscriptionId);

            // Extract billing cycle from subscription
            const billingCycle = stripeSubscription.items.data[0]?.price.recurring.interval === 'year'
                ? 'YEARLY'
                : 'MONTHLY';

            // Update the customer ID if needed
            if (company.stripeCustomerId !== customerId) {
                await prisma.company.update({
                    where: { id: companyId },
                    data: { stripeCustomerId: customerId },
                });
            }

            // Check if a subscription already exists for this company
            const existingSubscription = await prisma.subscription.findUnique({
                where: { companyId },
            });

            if (existingSubscription) {
                console.log(`Subscription already exists for company ${companyId}, updating...`);

                // Se era uma assinatura trial, encerrar o trial da empresa
                if (existingSubscription.status === 'TRIAL') {
                    console.log(`[STRIPE] Encerrando trial da empresa ${companyId} após upgrade`);
                    await prisma.company.update({
                        where: { id: companyId },
                        data: { 
                            isTrial: false, 
                            trialEnd: new Date() 
                        }
                    });
                }

                // Update existing subscription
                const updated = await prisma.subscription.update({
                    where: { id: existingSubscription.id },
                    data: {
                        status: 'ACTIVE',
                        billingCycle,
                        stripeCustomerId: customerId,
                        stripeSubscriptionId: subscriptionId,
                        stripeCurrentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
                        cancelAtPeriodEnd: false,
                        // Atualizar o preço baseado nos metadados da sessão
                        pricePerMonth: new Prisma.Decimal(metadata.finalPrice || 0),
                        // Atualizar o limite de usuários baseado nos metadados da sessão
                        userLimit: parseInt(metadata.userLimit || 5),
                    },
                });

                // Log de auditoria para upgrade de trial
                if (existingSubscription.status === 'TRIAL') {
                    await safelyCreateAuditLog({
                        action: "UPDATE",
                        entityType: "Subscription",
                        entityId: updated.id,
                        details: {
                            action: 'trial_upgrade',
                            previousStatus: 'TRIAL',
                            newStatus: 'ACTIVE',
                            billingCycle,
                            userLimit: metadata.userLimit,
                            finalPrice: metadata.finalPrice
                        },
                        companyId,
                    });
                }

                // Registrar venda do afiliado se houver código
                if (metadata.affiliateCode) {
                    try {
                        console.log(`[STRIPE] Registrando venda para afiliado: ${metadata.affiliateCode}`);
                        
                        // Busca o afiliado
                        const affiliate = await prisma.affiliate.findUnique({
                            where: { code: metadata.affiliateCode.toUpperCase() }
                        });
                        
                        if (affiliate && affiliate.active) {
                            // Calcula a comissão
                            const saleAmount = parseFloat(metadata.finalPrice || 0);
                            const commission = (saleAmount * affiliate.commission) / 100;
                            
                            // Registra a venda
                            await prisma.affiliateSale.create({
                                data: {
                                    affiliateId: affiliate.id,
                                    companyId,
                                    subscriptionId: updated.id,
                                    saleAmount,
                                    commission
                                }
                            });
                            
                            // Atualiza estatísticas do afiliado
                            await prisma.affiliate.update({
                                where: { id: affiliate.id },
                                data: {
                                    totalEarnings: { increment: commission },
                                    totalSales: { increment: 1 }
                                }
                            });
                            
                            console.log(`[STRIPE] Venda registrada para afiliado ${affiliate.name}: R$ ${commission.toFixed(2)}`);
                        } else {
                            console.log(`[STRIPE] Afiliado não encontrado ou inativo: ${metadata.affiliateCode}`);
                        }
                    } catch (error) {
                        console.error('[STRIPE] Erro ao registrar venda do afiliado:', error);
                        // Não falha o processo principal se houver erro no registro do afiliado
                    }
                }

                return updated;
            }

            // Extract active modules from subscription items
            const subscribedProducts = stripeSubscription.items.data.map(item => item.price.product);

            // Get all module types to include (basic modules + subscribed products)
            const moduleTypes = [...BASIC_INCLUDED_MODULES];

            // Add premium modules based on subscribed products
            // Here, you would map Stripe products to SystemModule types
            // This is a placeholder - implement your actual logic
            for (const product of subscribedProducts) {
                // Get product metadata from Stripe to identify the module
                const stripeProduct = await stripe.products.retrieve(product);
                const moduleMetadata = stripeProduct.metadata || {};

                if (moduleMetadata.moduleType && !moduleTypes.includes(moduleMetadata.moduleType)) {
                    moduleTypes.push(moduleMetadata.moduleType);
                }
            }

            // Create Subscription record in database
            const subscription = await prisma.subscription.create({
                data: {
                    companyId,
                    status: 'ACTIVE',
                    billingCycle,
                    stripeCustomerId: customerId,
                    stripeSubscriptionId: subscriptionId,
                    stripeCurrentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
                    // Atualizar o preço baseado nos metadados da sessão
                    pricePerMonth: new Prisma.Decimal(metadata.finalPrice || 0),
                    // Atualizar o limite de usuários baseado nos metadados da sessão
                    userLimit: parseInt(metadata.userLimit || 5),

                    // Create module records
                    modules: {
                        create: moduleTypes.map(moduleType => ({
                            moduleType,
                            active: true,
                            // Todos os módulos do plano básico têm preço 0 (incluídos no preço total)
                            pricePerMonth: 0,
                        })),
                    },
                },
            });

            // Use the helper method to safely create an audit log
            await safelyCreateAuditLog({
                action: "CREATE",
                entityType: "Subscription",
                entityId: subscription.id,
                details: {
                    billingCycle,
                    modules: moduleTypes,
                    userLimit: metadata.userLimit,
                    finalPrice: metadata.finalPrice
                },
                companyId,
            });

            // Registrar venda do afiliado se houver código
            if (metadata.affiliateCode) {
                try {
                    console.log(`[STRIPE] Registrando venda para afiliado: ${metadata.affiliateCode}`);
                    
                    // Busca o afiliado
                    const affiliate = await prisma.affiliate.findUnique({
                        where: { code: metadata.affiliateCode.toUpperCase() }
                    });
                    
                    if (affiliate && affiliate.active) {
                        // Calcula a comissão
                        const saleAmount = parseFloat(metadata.finalPrice || 0);
                        const commission = (saleAmount * affiliate.commission) / 100;
                        
                        // Registra a venda
                        await prisma.affiliateSale.create({
                            data: {
                                affiliateId: affiliate.id,
                                companyId,
                                subscriptionId: subscription.id,
                                saleAmount,
                                commission
                            }
                        });
                        
                        // Atualiza estatísticas do afiliado
                        await prisma.affiliate.update({
                            where: { id: affiliate.id },
                            data: {
                                totalEarnings: { increment: commission },
                                totalSales: { increment: 1 }
                            }
                        });
                        
                        console.log(`[STRIPE] Venda registrada para afiliado ${affiliate.name}: R$ ${commission.toFixed(2)}`);
                    } else {
                        console.log(`[STRIPE] Afiliado não encontrado ou inativo: ${metadata.affiliateCode}`);
                    }
                } catch (error) {
                    console.error('[STRIPE] Erro ao registrar venda do afiliado:', error);
                    // Não falha o processo principal se houver erro no registro do afiliado
                }
            }

            return subscription;
        } catch (error) {
            console.error('Erro ao processar checkout completo:', error);
            throw error;
        }
    }

    /**
     * Adiciona um módulo a uma assinatura existente
     */
    async addModuleToSubscription(companyId, moduleType) {
        try {
            // Verifica se a empresa existe e tem uma assinatura ativa
            const company = await prisma.company.findUnique({
                where: { id: companyId },
                include: {
                    subscription: {
                        include: {
                            modules: true,
                        },
                    },
                },
            });

            if (!company) {
                throw new Error(`Empresa não encontrada: ${companyId}`);
            }

            if (!company.subscription || !company.subscription.active) {
                throw new Error('Empresa não possui assinatura ativa');
            }

            // Verifica se o módulo já está ativo
            const existingModule = company.subscription.modules.find(
                (m) => m.moduleType === moduleType && m.active
            );

            if (existingModule) {
                throw new Error(`Módulo ${moduleType} já está ativo na assinatura`);
            }

            // Verifica se é um ID de teste (não faz chamadas reais para o Stripe)
            const isTestSubscription = company.subscription.stripeSubscriptionId?.startsWith('sub_test_');

            let price = null;

            if (!isTestSubscription) {
                // Obtém o preço para o módulo
                price = await this.getPriceForModule(
                    moduleType,
                    company.subscription.billingCycle
                );

                // Obtém a assinatura no Stripe
                const stripeSubscription = await stripe.subscriptions.retrieve(
                    company.subscription.stripeSubscriptionId
                );

                // Adiciona o item à assinatura do Stripe
                await stripe.subscriptionItems.create({
                    subscription: stripeSubscription.id,
                    price: price.id,
                    quantity: 1,
                });
            } else {
                console.log(`[TEST MODE] Simulando adição do módulo ${moduleType} à assinatura de teste`);
            }

            // Adiciona ou atualiza o módulo no banco de dados
            const moduleInDb = await prisma.subscriptionModule.findFirst({
                where: {
                    subscriptionId: company.subscription.id,
                    moduleType: moduleType,
                },
            });

            if (moduleInDb) {
                // Atualiza o módulo existente
                await prisma.subscriptionModule.update({
                    where: { id: moduleInDb.id },
                    data: {
                        active: true,
                        stripePriceId: price?.id || null, // Para test subscriptions, price pode ser null
                        pricePerMonth: new Prisma.Decimal(0), // Módulos básicos incluídos no preço total
                        addedAt: new Date(),
                    },
                });
            } else {
                // Cria um novo registro de módulo
                await prisma.subscriptionModule.create({
                    data: {
                        subscriptionId: company.subscription.id,
                        moduleType: moduleType,
                        active: true,
                        stripePriceId: price?.id || null, // Para test subscriptions, price pode ser null
                        pricePerMonth: new Prisma.Decimal(0), // Módulos básicos incluídos no preço total
                    },
                });
            }

            // Para módulos básicos, não altera o preço (já incluído)
            // Para módulos adicionais (que não vendemos mais), preço permanece o mesmo
            const currentPrice = parseFloat(company.subscription.pricePerMonth);

            await prisma.subscription.update({
                where: { id: company.subscription.id },
                data: {
                    pricePerMonth: new Prisma.Decimal(currentPrice), // Mantém o preço atual
                },
            });

            // Registra a ação no log de auditoria - será feito pelo controller
            // Não criamos log aqui para evitar problemas com userId null

            return {
                success: true,
                message: `Módulo ${MODULES[moduleType].name} adicionado com sucesso`,
            };
        } catch (error) {
            console.error('Erro ao adicionar módulo à assinatura:', error);
            throw error;
        }
    }

    /**
     * Remove um módulo de uma assinatura
     * Nota: BASIC, ADMIN e SCHEDULING não podem ser removidos
     */
    async removeModuleFromSubscription(companyId, moduleType) {
        try {
            // Verifica se é um módulo que não pode ser removido
            if ([SystemModule.BASIC, SystemModule.ADMIN, SystemModule.SCHEDULING].includes(moduleType)) {
                throw new Error(`O módulo ${moduleType} não pode ser removido da assinatura`);
            }

            // Verifica se a empresa existe e tem uma assinatura ativa
            const company = await prisma.company.findUnique({
                where: { id: companyId },
                include: {
                    subscription: {
                        include: {
                            modules: true,
                        },
                    },
                },
            });

            if (!company) {
                throw new Error(`Empresa não encontrada: ${companyId}`);
            }

            if (!company.subscription || !company.subscription.active) {
                throw new Error('Empresa não possui assinatura ativa');
            }

            // Verifica se o módulo está ativo
            const existingModule = company.subscription.modules.find(
                (m) => m.moduleType === moduleType && m.active
            );

            if (!existingModule) {
                throw new Error(`Módulo ${moduleType} não está ativo na assinatura`);
            }

            // Verifica se é um ID de teste (não faz chamadas reais para o Stripe)
            const isTestSubscription = company.subscription.stripeSubscriptionId?.startsWith('sub_test_');

            if (!isTestSubscription) {
                // Obtém a assinatura no Stripe (apenas para IDs reais)
                const stripeSubscription = await stripe.subscriptions.retrieve(
                    company.subscription.stripeSubscriptionId,
                    { expand: ['items.data.price'] }
                );

                // Encontra o item da assinatura correspondente ao módulo
                const subscriptionItem = stripeSubscription.items.data.find(
                    (item) => item.price.metadata.moduleType === moduleType
                );

                if (subscriptionItem) {
                    // Remove o item da assinatura do Stripe
                    await stripe.subscriptionItems.del(subscriptionItem.id);
                }
            } else {
                console.log(`[TEST MODE] Simulando remoção do módulo ${moduleType} da assinatura de teste`);
            }

            // Desativa o módulo no banco de dados
            await prisma.subscriptionModule.update({
                where: { id: existingModule.id },
                data: {
                    active: false,
                },
            });

            // Atualiza o preço total da assinatura
            const newPrice = parseFloat(company.subscription.pricePerMonth) - parseFloat(existingModule.pricePerMonth);

            await prisma.subscription.update({
                where: { id: company.subscription.id },
                data: {
                    pricePerMonth: new Prisma.Decimal(Math.max(0, newPrice)), // Garante que o preço não seja negativo
                },
            });

            // Registra a ação no log de auditoria - será feito pelo controller
            // Não criamos log aqui para evitar problemas com userId null

            return {
                success: true,
                message: `Módulo ${MODULES[moduleType].name} removido com sucesso`,
            };
        } catch (error) {
            console.error('Erro ao remover módulo da assinatura:', error);
            throw error;
        }
    }

    /**
     * Cancela uma assinatura
     */
    async cancelSubscription(companyId, cancelAtPeriodEnd = true) {
        try {
            // Verifica se a empresa existe e tem uma assinatura ativa
            const company = await prisma.company.findUnique({
                where: { id: companyId },
                include: {
                    subscription: true,
                },
            });

            if (!company) {
                throw new Error(`Empresa não encontrada: ${companyId}`);
            }

            if (!company.subscription || !company.subscription.active) {
                throw new Error('Empresa não possui assinatura ativa');
            }

            // Verifica se é um ID de teste (não faz chamadas reais para o Stripe)
            const isTestSubscription = company.subscription.stripeSubscriptionId?.startsWith('sub_test_');

            if (!isTestSubscription) {
                // Cancela a assinatura no Stripe (apenas para IDs reais)
                await stripe.subscriptions.update(company.subscription.stripeSubscriptionId, {
                    cancel_at_period_end: cancelAtPeriodEnd,
                });
            } else {
                console.log(`[TEST MODE] Simulando cancelamento da assinatura de teste`);
            }

            // Atualiza o status da assinatura no banco de dados
            await prisma.subscription.update({
                where: { id: company.subscription.id },
                data: {
                    cancelAtPeriodEnd: cancelAtPeriodEnd,
                    // Se cancelAtPeriodEnd é false, então o cancelamento é imediato
                    status: cancelAtPeriodEnd ? SubscriptionStatus.ACTIVE : SubscriptionStatus.CANCELED,
                    active: cancelAtPeriodEnd, // Mantém ativo se o cancelamento for no fim do período
                    endDate: cancelAtPeriodEnd ? null : new Date(),
                },
            });

            // Registra a ação no log de auditoria - será feito pelo controller
            // Não criamos log aqui para evitar problemas com userId null

            return {
                success: true,
                message: cancelAtPeriodEnd
                    ? 'Assinatura será cancelada no fim do período atual'
                    : 'Assinatura cancelada com sucesso',
            };
        } catch (error) {
            console.error('Erro ao cancelar assinatura:', error);
            throw error;
        }
    }

    /**
     * Processa webhooks do Stripe
     */
    async handleWebhook(event) {
        try {
            // Check the event type and call the appropriate handler
            switch (event.type) {
                case 'checkout.session.completed':
                    return await this.handleCheckoutCompleted(event);

                case 'customer.subscription.updated':
                    return await this.handleCustomerSubscriptionUpdated(event);

                case 'customer.subscription.deleted':
                    return await this.handleCustomerSubscriptionDeleted(event);

                case 'invoice.paid':
                    return await this.handleInvoicePaid(event);

                // Eventos específicos do PIX
                case 'payment_intent.succeeded':
                    return await this.handlePaymentIntentSucceeded(event);

                case 'payment_intent.payment_failed':
                    return await this.handlePaymentIntentFailed(event);

                case 'checkout.session.expired':
                    return await this.handleCheckoutExpired(event);

                // Add more event handlers here as needed

                default:
                    console.log(`Evento não tratado: ${event.type}`);
                    return;
            }
        } catch (error) {
            console.error(`Erro ao processar webhook do Stripe (${event.type}):`, error);
            throw error;
        }
    }
    /**
     * Processa o evento de invoice paga
     */
    async handleInvoicePaid(event) {
        try {
            const invoice = event.data.object;
            const stripeSubscriptionId = invoice.subscription;

            if (!stripeSubscriptionId) {
                console.log('Invoice not related to a subscription');
                return;
            }

            // Find the subscription in our database
            const dbSubscription = await prisma.subscription.findFirst({
                where: { stripeSubscriptionId }
            });

            if (!dbSubscription) {
                console.error(`Subscription not found for Stripe ID: ${stripeSubscriptionId}`);
                return;
            }

            // Create an invoice record
            const newInvoice = await prisma.invoice.create({
                data: {
                    subscriptionId: dbSubscription.id,
                    companyId: dbSubscription.companyId,
                    amount: Number(invoice.amount_paid) / 100, // Convert from cents
                    status: 'PAID',
                    dueDate: new Date(invoice.due_date * 1000 || Date.now()),
                    paidAt: new Date(invoice.status_transitions.paid_at * 1000 || Date.now()),
                    stripeInvoiceId: invoice.id,
                    stripeInvoiceUrl: invoice.hosted_invoice_url,
                },
            });

            // Encerrar trial da empresa, se estiver ativa
            await prisma.company.update({
                where: { id: dbSubscription.companyId },
                data: { isTrial: false, trialEnd: new Date() }
            });

            // Use the helper method to safely create an audit log
            await safelyCreateAuditLog({
                action: "CREATE",
                entityType: "Invoice",
                entityId: newInvoice.id,
                details: {
                    amount: Number(invoice.amount_paid) / 100,
                    status: 'PAID',
                },
                companyId: dbSubscription.companyId,
            });

            return newInvoice;
        } catch (error) {
            console.error('Erro ao processar fatura paga:', error);
            throw error;
        }
    }

    // Handle customer subscription updated webhook
    async handleCustomerSubscriptionUpdated(event) {
        try {
            const subscription = event.data.object;
            const stripeSubscriptionId = subscription.id;

            // Find the subscription in our database
            const dbSubscription = await prisma.subscription.findFirst({
                where: { stripeSubscriptionId },
                include: { modules: true }
            });

            if (!dbSubscription) {
                console.error(`Subscription not found for Stripe ID: ${stripeSubscriptionId}`);
                return;
            }

            // Extract billing cycle and status
            const billingCycle = subscription.items.data[0]?.plan.interval === 'year'
                ? 'YEARLY'
                : 'MONTHLY';

            // Map Stripe status to our status
            let status = 'ACTIVE';
            if (subscription.status === 'past_due') status = 'PAST_DUE';
            else if (subscription.status === 'canceled') status = 'CANCELED';
            else if (subscription.status === 'incomplete') status = 'INCOMPLETE';
            else if (subscription.status === 'trialing') status = 'TRIAL';

            // Update the subscription
            const updated = await prisma.subscription.update({
                where: { id: dbSubscription.id },
                data: {
                    status,
                    billingCycle,
                    cancelAtPeriodEnd: subscription.cancel_at_period_end,
                    stripeCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
                },
            });

            // Use the helper method to safely create an audit log
            await safelyCreateAuditLog({
                action: "UPDATE",
                entityType: "Subscription",
                entityId: dbSubscription.id,
                details: {
                    status,
                    billingCycle,
                    cancelAtPeriodEnd: subscription.cancel_at_period_end,
                },
                companyId: dbSubscription.companyId,
            });

            return updated;
        } catch (error) {
            console.error('Erro ao processar atualização de assinatura:', error);
            throw error;
        }
    }

    // Handle customer subscription deleted webhook
    async handleCustomerSubscriptionDeleted(event) {
        try {
            const subscription = event.data.object;
            const stripeSubscriptionId = subscription.id;

            // Find the subscription in our database
            const dbSubscription = await prisma.subscription.findFirst({
                where: { stripeSubscriptionId }
            });

            if (!dbSubscription) {
                console.error(`Subscription not found for Stripe ID: ${stripeSubscriptionId}`);
                return;
            }

            // Update the subscription status
            const updated = await prisma.subscription.update({
                where: { id: dbSubscription.id },
                data: {
                    status: 'CANCELED',
                    endDate: new Date(),
                },
            });

            // Use the helper method to safely create an audit log
            await safelyCreateAuditLog({
                action: "UPDATE",
                entityType: "Subscription",
                entityId: dbSubscription.id,
                details: {
                    status: 'CANCELED',
                    endDate: new Date().toISOString(),
                },
                companyId: dbSubscription.companyId,
            });

            return updated;
        } catch (error) {
            console.error('Erro ao processar exclusão de assinatura:', error);
            throw error;
        }
    }

    /**
     * Verifica se uma empresa tem acesso a um determinado módulo
     */
    async hasModuleAccess(companyId, moduleType) {
        try {
            // SYSTEM_ADMIN sempre tem acesso a tudo
            if (!companyId) {
                return true;
            }

            // Busca a assinatura da empresa
            const subscription = await prisma.subscription.findUnique({
                where: { companyId },
                include: {
                    modules: {
                        where: { active: true },
                    },
                },
            });

            // Se não tiver assinatura ou não estiver ativa, não tem acesso
            if (!subscription || !subscription.active) {
                return false;
            }

            // Verifica se o módulo está ativo
            return subscription.modules.some(m => m.moduleType === moduleType);
        } catch (error) {
            console.error('Erro ao verificar acesso ao módulo:', error);
            throw error;
        }
    }

    /**
     * Handler para payment_intent.succeeded (específico para PIX)
     */
    async handlePaymentIntentSucceeded(event) {
        try {
            const paymentIntent = event.data.object;
            const charges = paymentIntent.charges?.data || [];
            
            // Verifica se é pagamento PIX
            const pixCharge = charges.find(charge => 
                charge.payment_method_details?.type === 'pix'
            );

            if (pixCharge) {
                console.log(`[STRIPE PIX] Payment Intent PIX bem-sucedido: ${paymentIntent.id}`);
                console.log(`[STRIPE PIX] Valor: R$ ${(paymentIntent.amount / 100).toFixed(2)}`);
                
                // Se há metadata de sessionId, é um pagamento de signup
                if (paymentIntent.metadata?.sessionId) {
                    console.log(`[STRIPE PIX] Pagamento PIX de signup detectado para sessão: ${paymentIntent.metadata.sessionId}`);
                    // Aqui podemos adicionar lógica específica para finalizar o signup via PIX
                }
                
                // Log adicional para auditoria
                console.log(`[STRIPE PIX] Detalhes do PIX:`, {
                    id: pixCharge.id,
                    amount: paymentIntent.amount / 100,
                    currency: paymentIntent.currency,
                    status: paymentIntent.status
                });
            }

            return;
        } catch (error) {
            console.error('Erro ao processar payment_intent.succeeded:', error);
            throw error;
        }
    }

    /**
     * Handler para payment_intent.payment_failed (específico para PIX)
     */
    async handlePaymentIntentFailed(event) {
        try {
            const paymentIntent = event.data.object;
            const lastCharge = paymentIntent.last_payment_error;
            
            console.log(`[STRIPE PIX] Payment Intent falhou: ${paymentIntent.id}`);
            console.log(`[STRIPE PIX] Motivo: ${lastCharge?.message || 'Não especificado'}`);
            
            // Se há metadata de sessionId, é um pagamento de signup que falhou
            if (paymentIntent.metadata?.sessionId) {
                console.log(`[STRIPE PIX] Pagamento PIX de signup falhou para sessão: ${paymentIntent.metadata.sessionId}`);
                // Aqui podemos adicionar lógica para notificar sobre falha
            }

            return;
        } catch (error) {
            console.error('Erro ao processar payment_intent.payment_failed:', error);
            throw error;
        }
    }

    /**
     * Handler para checkout.session.expired (importante para PIX)
     */
    async handleCheckoutExpired(event) {
        try {
            const session = event.data.object;
            const metadata = session.metadata || {};
            
            console.log(`[STRIPE] Sessão de checkout expirada: ${session.id}`);
            
            // Se há sessionId, é um signup que expirou
            if (metadata.sessionId) {
                console.log(`[STRIPE] Sessão de signup expirada: ${metadata.sessionId}`);
                
                // Opcional: marcar a sessão de signup como expirada
                try {
                    await prisma.signupSession.update({
                        where: { id: metadata.sessionId },
                        data: { 
                            expiresAt: new Date() // Marca como expirada
                        }
                    });
                } catch (error) {
                    console.error('Erro ao marcar sessão de signup como expirada:', error);
                }
            }

            return;
        } catch (error) {
            console.error('Erro ao processar checkout.session.expired:', error);
            throw error;
        }
    }
}

module.exports = new StripeService();