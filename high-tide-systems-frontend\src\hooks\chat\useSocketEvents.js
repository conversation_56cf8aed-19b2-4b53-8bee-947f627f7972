import { useEffect, useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';

export const useSocketEvents = (
  socket, 
  isConnected, 
  { 
    addMessage, 
    setUnreadCount, 
    loadUnreadCount,
    loadConversations,
    updateConversationLastMessage
  }
) => {
  const { user } = useAuth();

  // Configurar eventos do socket
  const setupSocketEvents = useCallback(() => {
    if (!socket || !isConnected) return;

    console.log('Configurando eventos do WebSocket...');

    // Evento: Nova mensagem recebida
    const handleNewMessage = (data) => {
      console.log('Nova mensagem recebida via WebSocket:', data);
      
      if (data?.message) {
        // Adicionar mensagem ao estado
        addMessage(data.message);
        
        // Atualizar a última mensagem da conversa
        if (updateConversationLastMessage && data.message.conversationId) {
          updateConversationLastMessage(data.message.conversationId, data.message);
        }
        
        // Atualizar contagem de não lidas se a mensagem não for do usuário atual
        const isOwnMessage = data.message.senderId === user?.id || 
                           data.message.senderClientId === user?.id;
        
        if (!isOwnMessage) {
          loadUnreadCount(true);
        }
        
        // Disparar evento customizado para forçar atualização da UI
        window.dispatchEvent(new CustomEvent('chat:websocket:update', {
          detail: { 
            type: 'message', 
            action: 'new',
            conversationId: data.message.conversationId,
            timestamp: Date.now() 
          }
        }));
      }
    };

    // Evento: Atualização de mensagens não lidas
    const handleUnreadUpdate = (data) => {
      console.log('Atualização de contagem não lidas via WebSocket:', data);
      
      if (typeof data?.count === 'number') {
        setUnreadCount(data.count);
      } else if (typeof data?.unreadCount === 'number') {
        setUnreadCount(data.unreadCount);
      }
    };

    // Evento: Conversas atualizadas
    const handleConversationsUpdate = (data) => {
      console.log('Conversas atualizadas via WebSocket:', data);
      
      // Recarregar conversas sem usar cache
      loadConversations(true);
    };

    // Evento: Nova conversa criada
    const handleConversationCreated = (data) => {
      console.log('Nova conversa criada via WebSocket:', data);
      
      // Recarregar conversas para incluir a nova
      loadConversations(true);
    };

    // Evento: Participante removido
    const handleParticipantRemoved = (data) => {
      console.log('Participante removido via WebSocket:', data);
      
      // Se o usuário atual foi removido, recarregar conversas
      if (data?.userId === user?.id || data?.clientId === user?.id) {
        console.log('Usuário atual removido de uma conversa, recarregando...');
        loadConversations(true);
      }
    };

    // Registrar eventos
    socket.on('message:new', handleNewMessage);
    socket.on('unread:update', handleUnreadUpdate);
    socket.on('conversations:update', handleConversationsUpdate);
    socket.on('conversation:created', handleConversationCreated);
    socket.on('participant:removed', handleParticipantRemoved);

    // Cleanup function
    return () => {
      socket.off('message:new', handleNewMessage);
      socket.off('unread:update', handleUnreadUpdate);
      socket.off('conversations:update', handleConversationsUpdate);
      socket.off('conversation:created', handleConversationCreated);
      socket.off('participant:removed', handleParticipantRemoved);
    };
  }, [socket, isConnected, addMessage, setUnreadCount, loadUnreadCount, loadConversations, user?.id]);

  // Configurar eventos quando socket conectar
  useEffect(() => {
    if (socket && isConnected) {
      const cleanup = setupSocketEvents();
      return cleanup;
    }
  }, [socket, isConnected, setupSocketEvents]);

  // Emitir evento para entrar em sala de conversa
  const joinConversationRoom = useCallback((conversationId) => {
    if (socket?.connected && conversationId) {
      console.log(`Entrando na sala da conversa ${conversationId}`);
      socket.emit('join:conversation', { conversationId });
    }
  }, [socket]);

  // Emitir evento para sair de sala de conversa
  const leaveConversationRoom = useCallback((conversationId) => {
    if (socket?.connected && conversationId) {
      console.log(`Saindo da sala da conversa ${conversationId}`);
      socket.emit('leave:conversation', { conversationId });
    }
  }, [socket]);

  // Emitir evento de digitação
  const emitTyping = useCallback((conversationId, isTyping = true) => {
    if (socket?.connected && conversationId) {
      socket.emit('user:typing', {
        conversationId,
        isTyping,
        userId: user?.id
      });
    }
  }, [socket, user?.id]);

  return {
    joinConversationRoom,
    leaveConversationRoom,
    emitTyping,
    setupSocketEvents
  };
};