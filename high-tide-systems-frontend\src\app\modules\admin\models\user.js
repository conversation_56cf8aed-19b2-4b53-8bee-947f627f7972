export class User {
  constructor({
    id,
    login,
    email,
    fullName,
    cpf,
    cnpj,
    birthDate,
    address,
    streetNumber,
    complement,
    neighborhood,
    city,
    state,
    postalCode,
    phone,
    companyId,
    branchId,
    company,
    branch,
    professionId,
    professionObj,
    modules,
    permissions,
    role,
    active,
    profileImageUrl,
    profileImageFullUrl,
    createdAt,
    updatedAt,
    lastLoginAt
  }) {
    this.id = id;
    this.login = login;
    this.email = email;
    this.fullName = fullName;
    this.cpf = cpf;
    this.cnpj = cnpj;
    this.birthDate = birthDate ? new Date(birthDate) : null;
    this.address = address;
    this.streetNumber = streetNumber;
    this.complement = complement;
    this.neighborhood = neighborhood;
    this.city = city;
    this.state = state;
    this.postalCode = postalCode;
    this.phone = phone;
    this.companyId = companyId;
    this.branchId = branchId;
    this.company = company;
    this.branch = branch;
    this.professionId = professionId;
    this.professionObj = professionObj;
    this.modules = modules || [];
    this.permissions = permissions || [];
    this.role = role;
    this.active = active;
    this.profileImageUrl = profileImageUrl;
    this.profileImageFullUrl = profileImageFullUrl;
    this.createdAt = createdAt ? new Date(createdAt) : null;
    this.updatedAt = updatedAt ? new Date(updatedAt) : null;
    this.lastLoginAt = lastLoginAt ? new Date(lastLoginAt) : null;
  }

  // Format phone number for display
  get formattedPhone() {
    if (!this.phone) return '';
    
    const phone = this.phone.replace(/\D/g, '');
    if (phone.length === 11) {
      return `(${phone.substring(0, 2)}) ${phone.substring(2, 7)}-${phone.substring(7)}`;
    } else if (phone.length === 10) {
      return `(${phone.substring(0, 2)}) ${phone.substring(2, 6)}-${phone.substring(6)}`;
    }
    return this.phone;
  }

  // Format CPF for display
  get formattedCpf() {
    if (!this.cpf) return '';
    
    const cpf = this.cpf.replace(/\D/g, '');
    if (cpf.length === 11) {
      return `${cpf.substring(0, 3)}.${cpf.substring(3, 6)}.${cpf.substring(6, 9)}-${cpf.substring(9)}`;
    }
    return this.cpf;
  }

  // Format CNPJ for display
  get formattedCnpj() {
    if (!this.cnpj) return '';
    
    const cnpj = this.cnpj.replace(/\D/g, '');
    if (cnpj.length === 14) {
      return `${cnpj.substring(0, 2)}.${cnpj.substring(2, 5)}.${cnpj.substring(5, 8)}/${cnpj.substring(8, 12)}-${cnpj.substring(12)}`;
    }
    return this.cnpj;
  }

  // Format birthdate for display
  get formattedBirthDate() {
    if (!this.birthDate) return '';
    
    return this.birthDate.toLocaleDateString('pt-BR');
  }

  // Get full address
  get fullAddress() {
    const parts = [];
    if (this.address) {
      let addressPart = this.address;
      if (this.streetNumber) addressPart += `, ${this.streetNumber}`;
      if (this.complement) addressPart += ` - ${this.complement}`;
      parts.push(addressPart);
    }
    if (this.neighborhood) parts.push(this.neighborhood);
    if (this.city) parts.push(this.city);
    if (this.state) parts.push(this.state);
    if (this.postalCode) parts.push(this.postalCode);
    
    return parts.join(', ');
  }

  // Check if user has a specific module
  hasModule(module) {
    return this.modules.includes(module);
  }

  // Check if user has a specific permission
  hasPermission(permission) {
    return this.permissions.includes(permission);
  }

  // Check if user is a system admin
  get isSystemAdmin() {
    return this.role === 'SYSTEM_ADMIN';
  }

  // Check if user is a company admin
  get isCompanyAdmin() {
    return this.role === 'COMPANY_ADMIN';
  }

  // Get user's profession name
  get professionName() {
    return this.professionObj?.name || 'Sem profissão';
  }

  // Get user's company name
  get companyName() {
    return this.company?.name || 'Sem empresa';
  }

  // Get user's branch name
  get branchName() {
    return this.branch?.name || 'Sem unidade';
  }
}

export default User;
