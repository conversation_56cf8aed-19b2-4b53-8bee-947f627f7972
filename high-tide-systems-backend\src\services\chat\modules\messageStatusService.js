// src/services/chat/modules/messageStatusService.js
const prisma = require('../../../utils/prisma');
const { validateConversationAccess, getUserData } = require('./validationService');

/**
 * Cria status de mensagem para todos os participantes de uma conversa
 * Versão otimizada que faz uma única query em batch
 * @param {string} messageId - ID da mensagem
 * @param {string} conversationId - ID da conversa
 * @param {string} senderId - ID do remetente (para não criar status para ele mesmo)
 * @returns {Promise<Array>} - Status criados
 */
const createMessageStatusForParticipants = async (messageId, conversationId, senderId) => {
  try {
    console.log(`[MessageStatusService] Criando status para mensagem ${messageId}, conversa ${conversationId}, remetente ${senderId}`);

    // Primeiro, vamos ver todos os participantes da conversa
    const allParticipants = await prisma.conversationParticipant.findMany({
      where: {
        conversationId,
        leftAt: null
      },
      select: { id: true, userId: true, clientId: true }
    });

    console.log(`[MessageStatusService] Todos os participantes da conversa:`, allParticipants);
    console.log(`[MessageStatusService] Remetente ID: ${senderId}`);

    // Filtrar participantes excluindo o remetente
    const participants = allParticipants.filter(participant => {
      const isRemetente = participant.userId === senderId || participant.clientId === senderId;
      return !isRemetente;
    });

    console.log(`[MessageStatusService] Participantes após filtrar remetente:`, participants);

    if (participants.length === 0) {
      console.log(`[MessageStatusService] Nenhum participante encontrado para criar status`);
      return [];
    }

    // Criar status em batch para todos os participantes
    const statusData = participants.map(participant => ({
      messageId,
      participantId: participant.id,
      status: 'SENT'
    }));

    console.log(`[MessageStatusService] Dados do status a serem criados:`, statusData);

    const createdStatuses = await prisma.messageStatus.createMany({
      data: statusData,
      skipDuplicates: true
    });

    console.log(`[MessageStatusService] ${createdStatuses.count} status de mensagem criados`);

    // Verificar se os status foram realmente criados
    const verifyStatuses = await prisma.messageStatus.findMany({
      where: { messageId },
      include: { participant: true }
    });
    console.log(`[MessageStatusService] Status verificados no banco:`, verifyStatuses);

    return createdStatuses;
  } catch (error) {
    console.error('[MessageStatusService] Erro ao criar status:', error);
    throw error;
  }
};

/**
 * Atualiza o status de uma mensagem para um participante
 * @param {string} messageId - ID da mensagem
 * @param {string} participantId - ID do participante
 * @param {string} status - Novo status (SENT, DELIVERED, READ)
 * @returns {Promise<Object>} - Status atualizado
 */
const updateMessageStatus = async (messageId, participantId, status) => {
  try {
    const messageStatus = await prisma.messageStatus.upsert({
      where: {
        messageId_participantId: {
          messageId,
          participantId
        }
      },
      create: {
        messageId,
        participantId,
        status
      },
      update: {
        status,
        timestamp: new Date()
      }
    });

    return messageStatus;
  } catch (error) {
    console.error('[MessageStatusService] Erro ao atualizar status:', error);
    throw error;
  }
};

/**
 * Marca mensagens como lidas para um usuário em uma conversa
 * @param {string} userId - ID do usuário
 * @param {string} conversationId - ID da conversa
 * @param {string} messageId - ID da última mensagem lida
 * @returns {Promise<number>} - Número de mensagens marcadas como lidas
 */
const markMessagesAsRead = async (userId, conversationId, messageId) => {
  try {
    // Validar acesso
    await validateConversationAccess(conversationId, userId);

    // Buscar o participante
    const participant = await prisma.conversationParticipant.findFirst({
      where: {
        conversationId,
        OR: [
          { userId },
          { clientId: userId }
        ],
        leftAt: null
      }
    });

    if (!participant) {
      throw new Error('Participante não encontrado na conversa');
    }

    // Buscar a mensagem para obter timestamp
    const message = await prisma.message.findUnique({
      where: { id: messageId },
      select: { createdAt: true }
    });

    if (!message) {
      throw new Error('Mensagem não encontrada');
    }

    // Buscar mensagens não lidas até a especificada
    const unreadMessages = await prisma.message.findMany({
      where: {
        conversationId,
        createdAt: { lte: message.createdAt },
        isDeleted: false,
        NOT: {
          OR: [
            { senderId: userId },
            { senderClientId: userId }
          ]
        },
        statuses: {
          none: {
            participantId: participant.id,
            status: 'READ'
          }
        }
      },
      select: { id: true }
    });

    // Criar status de leitura para todas as mensagens
    if (unreadMessages.length > 0) {
      await prisma.messageStatus.createMany({
        data: unreadMessages.map(msg => ({
          messageId: msg.id,
          participantId: participant.id,
          status: 'READ',
          timestamp: new Date()
        })),
        skipDuplicates: true
      });
    }

    // Atualizar a última mensagem lida do participante
    await prisma.conversationParticipant.update({
      where: { id: participant.id },
      data: { lastReadMessageId: messageId }
    });

    console.log(`[MessageStatusService] ${unreadMessages.length} mensagens marcadas como lidas para usuário ${userId}`);
    return unreadMessages.length;
  } catch (error) {
    console.error('[MessageStatusService] Erro ao marcar mensagens como lidas:', error);
    throw error;
  }
};

/**
 * Busca mensagens não lidas para um usuário usando Prisma ORM
 * @param {string} userId - ID do usuário
 * @returns {Promise<Object>} - Dados das mensagens não lidas
 */
const getUnreadMessages = async (userId) => {
  try {
    const userData = await getUserData(userId);
    if (!userData) {
      return { conversations: [], totalUnread: 0 };
    }

    // Buscar conversas do usuário
    const conversations = await prisma.conversation.findMany({
      where: {
        isActive: true,
        participants: {
          some: {
            ...(userData.type === 'CLIENT' ? { clientId: userId } : { userId: userId }),
            leftAt: null
          }
        }
      },
      include: {
        participants: {
          where: { leftAt: null }
        }
      }
    });

    const conversationsWithUnread = [];
    let totalUnread = 0;

    for (const conversation of conversations) {
      const participant = conversation.participants.find(p => 
        (userData.type === 'CLIENT' ? p.clientId : p.userId) === userId
      );

      if (!participant) continue;

      // Contar mensagens não lidas
      console.log(`[MessageStatusService] Contando mensagens não lidas para participante ${participant.id} na conversa ${conversation.id}`);
      console.log(`[MessageStatusService] Tipo de usuário: ${userData.type}, userId: ${userId}`);

      // Construir condição para excluir mensagens do próprio usuário
      const excludeOwnMessages = userData.type === 'CLIENT'
        ? { senderClientId: { not: userId } }  // Se é cliente, excluir onde senderClientId = userId
        : { senderId: { not: userId } };       // Se é usuário, excluir onde senderId = userId

      const whereClause = {
        conversationId: conversation.id,
        isDeleted: false,
        ...excludeOwnMessages,
        statuses: {
          none: {
            participantId: participant.id,
            status: 'READ'
          }
        }
      };

      console.log(`[MessageStatusService] Where clause:`, JSON.stringify(whereClause, null, 2));

      // Primeiro, vamos ver todas as mensagens da conversa
      const allMessages = await prisma.message.findMany({
        where: {
          conversationId: conversation.id,
          isDeleted: false
        },
        select: {
          id: true,
          senderId: true,
          senderClientId: true,
          content: true,
          createdAt: true,
          statuses: {
            where: { participantId: participant.id },
            select: { status: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 5
      });

      console.log(`[MessageStatusService] Últimas 5 mensagens da conversa ${conversation.id}:`,
        allMessages.map(m => ({
          id: m.id,
          senderId: m.senderId,
          senderClientId: m.senderClientId,
          content: m.content.substring(0, 20),
          statuses: m.statuses
        }))
      );

      const unreadCount = await prisma.message.count({
        where: whereClause
      });

      console.log(`[MessageStatusService] Mensagens não lidas encontradas: ${unreadCount}`);

      if (unreadCount > 0) {
        conversationsWithUnread.push({
          conversationId: conversation.id,
          title: conversation.title,
          type: conversation.type,
          lastMessageAt: conversation.lastMessageAt,
          unreadCount
        });
        totalUnread += unreadCount;
      }
    }

    console.log(`[MessageStatusService] ${totalUnread} mensagens não lidas encontradas para usuário ${userId}`);

    return {
      conversations: conversationsWithUnread,
      totalUnread
    };
  } catch (error) {
    console.error('[MessageStatusService] Erro ao buscar mensagens não lidas:', error);
    return { conversations: [], totalUnread: 0 };
  }
};

/**
 * Busca contagem total de mensagens não lidas usando Prisma ORM
 * @param {string} userId - ID do usuário
 * @returns {Promise<number>} - Número total de mensagens não lidas
 */
const getUnreadCount = async (userId) => {
  try {
    const userData = await getUserData(userId);
    if (!userData) return 0;

    // Buscar participações do usuário
    const participants = await prisma.conversationParticipant.findMany({
      where: {
        ...(userData.type === 'CLIENT' ? { clientId: userId } : { userId: userId }),
        leftAt: null,
        conversation: {
          isActive: true
        }
      },
      select: { id: true, conversationId: true }
    });

    if (participants.length === 0) return 0;

    let totalUnread = 0;

    // Para cada participação, contar mensagens não lidas
    for (const participant of participants) {
      console.log(`[MessageStatusService] Verificando participante ${participant.id} na conversa ${participant.conversationId}`);

      const unreadCount = await prisma.message.count({
        where: {
          conversationId: participant.conversationId,
          isDeleted: false,
          NOT: {
            OR: [
              { senderId: userData.type === 'CLIENT' ? null : userId },
              { senderClientId: userData.type === 'CLIENT' ? userId : null }
            ]
          },
          statuses: {
            none: {
              participantId: participant.id,
              status: 'READ'
            }
          }
        }
      });

      console.log(`[MessageStatusService] Participante ${participant.id}: ${unreadCount} mensagens não lidas`);
      totalUnread += unreadCount;
    }

    console.log(`[MessageStatusService] ${totalUnread} mensagens não lidas para usuário ${userId}`);
    return totalUnread;
  } catch (error) {
    console.error('[MessageStatusService] Erro ao contar mensagens não lidas:', error);
    return 0;
  }
};

/**
 * Reset de todas as mensagens não lidas para um usuário usando Prisma ORM
 * @param {string} userId - ID do usuário
 * @returns {Promise<boolean>} - true se sucesso
 */
const resetAllUnreadMessages = async (userId) => {
  try {
    const userData = await getUserData(userId);
    if (!userData) return false;

    // Buscar participações do usuário
    const participants = await prisma.conversationParticipant.findMany({
      where: {
        ...(userData.type === 'CLIENT' ? { clientId: userId } : { userId: userId }),
        leftAt: null,
        conversation: {
          isActive: true
        }
      },
      select: { id: true, conversationId: true }
    });

    if (participants.length === 0) return true;

    // Para cada participação, marcar mensagens como lidas
    for (const participant of participants) {
      // Buscar mensagens não lidas
      const unreadMessages = await prisma.message.findMany({
        where: {
          conversationId: participant.conversationId,
          isDeleted: false,
          NOT: {
            OR: [
              { senderId: userData.type === 'CLIENT' ? null : userId },
              { senderClientId: userData.type === 'CLIENT' ? userId : null }
            ]
          },
          statuses: {
            none: {
              participantId: participant.id,
              status: 'READ'
            }
          }
        },
        select: { id: true }
      });

      // Criar status de leitura para mensagens não lidas
      if (unreadMessages.length > 0) {
        await prisma.messageStatus.createMany({
          data: unreadMessages.map(msg => ({
            messageId: msg.id,
            participantId: participant.id,
            status: 'READ',
            timestamp: new Date()
          })),
          skipDuplicates: true
        });
      }
    }

    console.log(`[MessageStatusService] Mensagens marcadas como lidas para usuário ${userId}`);
    return true;
  } catch (error) {
    console.error('[MessageStatusService] Erro ao resetar mensagens não lidas:', error);
    return false;
  }
};

module.exports = {
  createMessageStatusForParticipants,
  updateMessageStatus,
  markMessagesAsRead,
  getUnreadMessages,
  getUnreadCount,
  resetAllUnreadMessages
};