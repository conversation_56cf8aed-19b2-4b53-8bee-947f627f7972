const prisma = require('../utils/prisma');

/**
 * Middleware para aplicar validações dinâmicas baseadas nas preferências da empresa
 * @param {string} entityType - Tipo de entidade (user, client, patient, scheduling, serviceType, location)
 * @param {boolean} isUpdate - Se é uma operação de atualização
 */
const dynamicValidation = (entityType, isUpdate = false) => async (req, res, next) => {
  try {
    // Se o usuário é SYSTEM_ADMIN e não tem companyId, pular validações baseadas em preferências da empresa
    if (req.user.role === 'SYSTEM_ADMIN' && !req.user.companyId) {
      console.log('[dynamicValidation] SYSTEM_ADMIN sem companyId - pulando validações baseadas em preferências da empresa');
      return next();
    }
    
    // Buscar preferências da empresa
    const companyPrefs = await prisma.companyPreference.findUnique({
      where: { companyId: req.user.companyId }
    });
    
    const preferences = companyPrefs?.preferences || {};
    const entityPrefs = preferences[entityType] || {};
    
    // Aplicar validações baseadas nas preferências
    const validationErrors = [];
    
    // Validações específicas por tipo de entidade
    if (entityType === 'user') {
      // Para atualizações, verificar se o campo já existe no banco
      if (isUpdate && req.params.id) {
        const existingUser = await prisma.user.findUnique({
          where: { id: req.params.id },
          select: { phone: true, cpf: true, cnpj: true, birthDate: true, postalCode: true, role: true, branchId: true }
        });
        
        // Para atualizações de perfil, ser mais flexível com validações
        // Só validar campos obrigatórios se eles não existirem no banco E não estiverem sendo enviados
        // E se não for uma atualização de perfil próprio
        const isOwnProfile = req.params.id === req.user.id;
        
        if (!isOwnProfile) {
          if (entityPrefs.userPhone && !req.body.phone && !existingUser?.phone) {
            validationErrors.push('Telefone é obrigatório');
          }
          if (entityPrefs.userCpfCnpj && !req.body.cpf && !req.body.cnpj && !existingUser?.cpf && !existingUser?.cnpj) {
            validationErrors.push('CPF/CNPJ é obrigatório');
          }
          if (entityPrefs.userBirthDate && !req.body.birthDate && !existingUser?.birthDate) {
            validationErrors.push('Data de nascimento é obrigatória');
          }
          if (entityPrefs.userCep && !req.body.postalCode && !existingUser?.postalCode) {
            validationErrors.push('CEP é obrigatório');
          }
          if (entityPrefs.userUnit && !req.body.branchId && !existingUser?.branchId) {
            validationErrors.push('Unidade é obrigatória');
          }
        }
      } else {
        // Para criação, validar normalmente
        if (entityPrefs.userCpfCnpj && !req.body.cpf && !req.body.cnpj) {
          validationErrors.push('CPF/CNPJ é obrigatório');
        }
        if (entityPrefs.userPhone && !req.body.phone) {
          validationErrors.push('Telefone é obrigatório');
        }
        if (entityPrefs.userBirthDate && !req.body.birthDate) {
          validationErrors.push('Data de nascimento é obrigatória');
        }
        if (entityPrefs.userCep && !req.body.postalCode) {
          validationErrors.push('CEP é obrigatório');
        }
        if (entityPrefs.userUnit && !req.body.branchId) {
          validationErrors.push('Unidade é obrigatória');
        }
      }
    }
    
    if (entityType === 'client') {
      // Para atualizações, verificar se o campo já existe no banco
      if (isUpdate && req.params.id) {
        // Para atualizações de cliente, não validar campos da pessoa aqui
        // pois a pessoa é atualizada separadamente através do personsService
        console.log('[dynamicValidation] Atualização de cliente - pulando validações de pessoa');
      } else {
        // Para criação, validar normalmente
        if (entityPrefs.clientCpfCnpj && !req.body.person?.cpf && !req.body.person?.cnpj) {
          validationErrors.push('CPF/CNPJ é obrigatório');
        }
        if (entityPrefs.clientPhone && !req.body.person?.phone) {
          validationErrors.push('Telefone é obrigatório');
        }
        if (entityPrefs.clientCep && !req.body.person?.postalCode) {
          validationErrors.push('CEP é obrigatório');
        }
      }
    }
    
    if (entityType === 'patient') {
      // Para atualizações, verificar se o campo já existe no banco
      if (isUpdate && req.params.id) {
        const existingPerson = await prisma.person.findUnique({
          where: { id: req.params.id },
          select: { 
            cpf: true, 
            birthDate: true, 
            gender: true, 
            email: true, 
            phone: true, 
            postalCode: true,
            clientPersons: {
              select: {
                clientId: true,
                isPrimary: true
              }
            }
          }
        });
        
        // Só validar se o campo não existir no banco E não estiver sendo enviado
        if (entityPrefs.patientCpfCnpj && !req.body.cpf && !existingPerson?.cpf) {
          validationErrors.push('CPF é obrigatório');
        }
        if (entityPrefs.patientBirthDate && !req.body.birthDate && !existingPerson?.birthDate) {
          validationErrors.push('Data de nascimento é obrigatória');
        }
        if (entityPrefs.patientGender && !req.body.gender && !existingPerson?.gender) {
          validationErrors.push('Gênero é obrigatório');
        }
        if (entityPrefs.patientEmail && (!req.body.email || req.body.email.trim() === '') && !existingPerson?.email) {
          validationErrors.push('E-mail é obrigatório');
        }
        if (entityPrefs.patientPhone && !req.body.phone && !existingPerson?.phone) {
          validationErrors.push('Telefone é obrigatório');
        }
        if (entityPrefs.patientCep && !req.body.postalCode && !existingPerson?.postalCode) {
          validationErrors.push('CEP é obrigatório');
        }
        if (entityPrefs.patientAssociateClient) {
          const hasClientId = req.body.clientId;
          const hasClientPersons = req.body.clientPersons && req.body.clientPersons.length > 0;
          const hasExistingClient = existingPerson?.clientPersons && existingPerson.clientPersons.length > 0;
          
          if (!hasClientId && !hasClientPersons && !hasExistingClient) {
            validationErrors.push('Associação com cliente é obrigatória');
          }
        }
      } else {
        // Para criação, validar normalmente
        if (entityPrefs.patientCpfCnpj && !req.body.cpf) {
          validationErrors.push('CPF é obrigatório');
        }
        if (entityPrefs.patientBirthDate && !req.body.birthDate) {
          validationErrors.push('Data de nascimento é obrigatória');
        }
        if (entityPrefs.patientGender && !req.body.gender) {
          validationErrors.push('Gênero é obrigatório');
        }
        if (entityPrefs.patientEmail && !req.body.email) {
          validationErrors.push('E-mail é obrigatório');
        }
        if (entityPrefs.patientPhone && !req.body.phone) {
          validationErrors.push('Telefone é obrigatório');
        }
        if (entityPrefs.patientCep && !req.body.postalCode) {
          validationErrors.push('CEP é obrigatório');
        }
        if (entityPrefs.patientAssociateClient) {
          const hasClientId = req.body.clientId;
          const hasClientPersons = req.body.clientPersons && req.body.clientPersons.length > 0;
          
          if (!hasClientId && !hasClientPersons) {
            validationErrors.push('Associação com cliente é obrigatória');
          }
        }
      }
    }

    // Validações de agendamento
    if (entityType === 'scheduling') {
      const schedulingPrefs = preferences.scheduling || {};
      
      // Validar campos obrigatórios baseado nas preferências
      if (schedulingPrefs.requiredFields) {
        const requiredFields = schedulingPrefs.requiredFields;

        // Verificar se o campo está habilitado nas preferências antes de validar
        if (requiredFields.insurance && schedulingPrefs.showInsurance !== false) {
          if (!req.body.insuranceId) {
            validationErrors.push('Convênio é obrigatório');
          }
        }

        if (requiredFields.serviceType && schedulingPrefs.showServiceTypes !== false) {
          if (!req.body.serviceTypeId) {
            validationErrors.push('Tipo de serviço é obrigatório');
          }
        }

        if (requiredFields.location && schedulingPrefs.showLocations !== false) {
          if (!req.body.locationId) {
            validationErrors.push('Local é obrigatório');
          }
        }

        if (requiredFields.professional) {
          if (!req.body.userId) {
            validationErrors.push('Profissional é obrigatório');
          }
        }

        if (requiredFields.title) {
          if (!req.body.title || req.body.title.trim() === '') {
            validationErrors.push('Título do agendamento é obrigatório');
          }
        }

        if (requiredFields.description) {
          if (!req.body.description || req.body.description.trim() === '') {
            validationErrors.push('Descrição é obrigatória');
          }
        }

        if (requiredFields.notes) {
          if (!req.body.notes || req.body.notes.trim() === '') {
            validationErrors.push('Observações são obrigatórias');
          }
        }
      }

      // Validar agendamento retroativo
      if (!schedulingPrefs.allowPastScheduling) {
        const startDate = new Date(req.body.startDate);
        const now = new Date();
        
        if (startDate < now) {
          validationErrors.push('Não é permitido agendar em datas passadas');
        }
      }

      // Validar dias da semana permitidos
      if (schedulingPrefs.selectedWeekDays && schedulingPrefs.selectedWeekDays.length > 0) {
        const startDate = new Date(req.body.startDate);
        const dayOfWeek = startDate.getDay(); // 0 = Domingo, 1 = Segunda, etc.
        
        if (!schedulingPrefs.selectedWeekDays.includes(dayOfWeek)) {
          const dayNames = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];
          validationErrors.push(`Agendamento não permitido em ${dayNames[dayOfWeek]}`);
        }
      }

      // Validar horários permitidos
      if (schedulingPrefs.allowedHours && schedulingPrefs.allowedHours.length === 24) {
        const startDate = new Date(req.body.startDate);
        const startHour = startDate.getHours();
        
        if (!schedulingPrefs.allowedHours[startHour]) {
          validationErrors.push(`Agendamento não permitido no horário ${String(startHour).padStart(2, '0')}:00`);
        }
      }

      // Validar múltiplos serviços
      if (!schedulingPrefs.allowMultipleServices) {
        // Verificar se há múltiplos agendamentos sendo criados
        if (req.body.sequentialSchedulings && req.body.sequentialSchedulings.length > 1) {
          validationErrors.push('Agendamento de múltiplos serviços não é permitido');
        }
      }

      // Validar confirmação do cliente
      if (schedulingPrefs.requireClientConfirmation) {
        if (!req.body.clientConfirmation) {
          validationErrors.push('Confirmação do cliente é obrigatória');
        }
      }

      // Validar recorrência
      if (schedulingPrefs.requireRecurrence) {
        if (!req.body.recurrencePattern) {
          validationErrors.push('Padrão de recorrência é obrigatório');
        }
      }

      // Validar agendamento sequencial
      if (schedulingPrefs.requireSequential) {
        if (!req.body.sequentialSchedulings || req.body.sequentialSchedulings.length === 0) {
          validationErrors.push('Agendamento sequencial é obrigatório');
        }
      }
    }

    // Validações de tipo de serviço
    if (entityType === 'serviceType') {
      const schedulingPrefs = preferences.scheduling || {};
      
      // Validar valor do serviço se obrigatório
      if (schedulingPrefs.requireServiceValue) {
        if (!req.body.value || req.body.value <= 0) {
          validationErrors.push('Valor do tipo de serviço é obrigatório');
        }
      }
    }

    // Validações de localização
    if (entityType === 'location') {
      const schedulingPrefs = preferences.scheduling || {};
      
      // Validar endereço da localização se obrigatório
      if (schedulingPrefs.requireLocationAddress) {
        if (!req.body.address || req.body.address.trim() === '') {
          validationErrors.push('Endereço da localização é obrigatório');
        }
      }

      // Validar telefone da localização se obrigatório
      if (schedulingPrefs.requireLocationPhone) {
        if (!req.body.phone || req.body.phone.trim() === '') {
          validationErrors.push('Telefone da localização é obrigatório');
        }
      }
    }
    
    if (validationErrors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Validação falhou',
        errors: validationErrors
      });
    }
    
    next();
  } catch (error) {
    console.error('Erro no middleware de validação dinâmica:', error);
    console.error('Stack trace:', error.stack);
    // Em caso de erro no middleware, continuar sem bloquear a requisição
    // mas registrar o erro para investigação
    next();
  }
};

module.exports = { dynamicValidation }; 