// src/socket/handlers/chatHandlers.js
const prisma = require('../../utils/prisma');
const chatService = require('../../services/chat/chatService');
const messageService = require('../../services/chat/messageService');

/**
 * Registra os handlers de eventos de chat para um socket
 * @param {Object} socket - Socket do cliente
 * @param {Object} io - Instância do servidor Socket.IO
 */
const register = (socket, io) => {
  // Entrar em salas de conversas do usuário
  joinUserConversations(socket);

  // Evento para enviar mensagem
  socket.on('message:send', async (data, callback) => {
    try {
      // Validar dados
      if (!data.conversationId) {
        return callback({ 
          success: false, 
          error: 'Dados inválidos. conversationId é obrigatório.' 
        });
      }

      // Para mensagens com anexos, o content pode estar vazio, mas pelo menos um deve estar presente
      const hasContent = data.content && data.content.trim() !== '';
      const hasAttachment = data.contentType === 'ATTACHMENT' && data.metadata && data.metadata.attachments;
      
      if (!hasContent && !hasAttachment) {
        return callback({ 
          success: false, 
          error: 'Dados inválidos. Conteúdo ou anexo é obrigatório.' 
        });
      }

      // Log para debug
      console.log('[Socket.IO] Message data received:', {
        conversationId: data.conversationId,
        contentType: data.contentType,
        hasContent: !!data.content,
        contentLength: data.content?.length || 0,
        hasMetadata: !!data.metadata
      });

      // Verificar se o usuário é participante da conversa
      const isParticipant = await chatService.isConversationParticipant(
        data.conversationId, 
        socket.user.id
      );

      if (!isParticipant) {
        return callback({ 
          success: false, 
          error: 'Você não é participante desta conversa.' 
        });
      }

      // Criar mensagem
      const message = await messageService.createMessage({
        conversationId: data.conversationId,
        senderId: socket.user.id,
        content: data.content,
        contentType: data.contentType || 'TEXT',
        referencedMessageId: data.referencedMessageId,
        metadata: data.metadata
      });

      // Atualizar última mensagem da conversa
      await chatService.updateLastMessage(data.conversationId, message.id);

      // Incluir o tempId na mensagem se existir para facilitar a substituição no frontend
      const messageWithTempId = data.tempId ? { ...message, tempId: data.tempId } : message;
      
      // Emitir evento para todos os participantes da conversa
      io.to(`conversation:${data.conversationId}`).emit('message:new', messageWithTempId);

      // Emitir atualização de mensagens não lidas para todos os participantes (exceto o remetente)
      await emitUnreadCountUpdate(io, data.conversationId, socket.user.id);

      // Retornar sucesso
      callback({ success: true, message });
    } catch (error) {
      console.error('[Socket.IO] Error sending message:', error);
      console.error('[Socket.IO] Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code
      });
      callback({ success: false, error: 'Erro ao enviar mensagem.' });
    }
  });

  // Evento para marcar mensagens como lidas
  socket.on('message:read', async (data, callback) => {
    try {
      // Validar dados
      if (!data.conversationId || !data.messageId) {
        if (typeof callback === 'function') {
          return callback({
            success: false,
            error: 'Dados inválidos. conversationId e messageId são obrigatórios.'
          });
        }
        return;
      }

      // Verificar se é cliente
      const isClient = socket.user.isClient || socket.user.role === 'CLIENT';
      
      // Verificar se o usuário é participante da conversa
      const participant = await prisma.conversationParticipant.findFirst({
        where: {
          conversationId: data.conversationId,
          OR: isClient ? [
            { clientId: socket.user.id, leftAt: null }
          ] : [
            { userId: socket.user.id, leftAt: null }
          ]
        }
      });

      if (!participant) {
        if (typeof callback === 'function') {
          return callback({
            success: false,
            error: 'Você não é participante desta conversa.'
          });
        }
        return;
      }

      // Atualizar última mensagem lida
      await chatService.updateLastReadMessage(
        participant.id, 
        data.messageId
      );

      // Emitir evento para todos os participantes da conversa
      io.to(`conversation:${data.conversationId}`).emit('message:read', {
        conversationId: data.conversationId,
        userId: socket.user.id,
        messageId: data.messageId
      });

      // Emitir atualização de mensagens não lidas apenas para o usuário que leu
      try {
        const unreadData = await messageService.getUnreadMessages(socket.user.id);
        socket.emit('unread:update', unreadData);
      } catch (error) {
        console.error('Error emitting unread update:', error);
      }

      // Retornar sucesso
      if (typeof callback === 'function') {
        callback({ success: true });
      }
    } catch (error) {
      console.error('[Socket.IO] Error marking message as read:', error);
      if (typeof callback === 'function') {
        callback({ success: false, error: 'Erro ao marcar mensagem como lida.' });
      }
    }
  });

  // Evento para indicar que o usuário está digitando
  socket.on('user:typing', async (data, callback) => {
    try {
      // Validar dados
      if (!data.conversationId) {
        return callback({ 
          success: false, 
          error: 'Dados inválidos. conversationId é obrigatório.' 
        });
      }

      // Verificar se o usuário é participante da conversa
      const isParticipant = await chatService.isConversationParticipant(
        data.conversationId, 
        socket.user.id
      );

      if (!isParticipant) {
        return callback({ 
          success: false, 
          error: 'Você não é participante desta conversa.' 
        });
      }

      // Emitir evento para todos os participantes da conversa, exceto o remetente
      socket.to(`conversation:${data.conversationId}`).emit('user:typing', {
        conversationId: data.conversationId,
        userId: socket.user.id,
        isTyping: data.isTyping || true
      });

      // Retornar sucesso
      callback({ success: true });
    } catch (error) {
      console.error('[Socket.IO] Error sending typing indicator:', error);
      callback({ success: false, error: 'Erro ao enviar indicador de digitação.' });
    }
  });
};

/**
 * Adiciona o socket às salas de todas as conversas do usuário
 * @param {Object} socket - Socket do cliente
 */
const joinUserConversations = async (socket) => {
  try {
    // Verificar se é cliente
    const isClient = socket.user.isClient || socket.user.role === 'CLIENT';
    
    // Buscar todas as conversas do usuário
    const participations = await prisma.conversationParticipant.findMany({
      where: isClient ? {
        clientId: socket.user.id,
        leftAt: null
      } : {
        userId: socket.user.id,
        leftAt: null
      },
      select: {
        conversationId: true
      }
    });

    // Entrar em cada sala de conversa
    for (const participation of participations) {
      socket.join(`conversation:${participation.conversationId}`);
    }

  } catch (error) {
    console.error('[Socket.IO] Error joining conversation rooms:', error);
  }
};

/**
 * Emite atualização de contagem de mensagens não lidas para os participantes
 * @param {Object} io - Instância do servidor Socket.IO
 * @param {string} conversationId - ID da conversa
 * @param {string} excludeUserId - ID do usuário a ser excluído (opcional)
 */
const emitUnreadCountUpdate = async (io, conversationId, excludeUserId = null) => {
  try {
    // Buscar todos os participantes da conversa
    const participants = await prisma.conversationParticipant.findMany({
      where: {
        conversationId,
        leftAt: null
      }
    });

    // Para cada participante, calcular e emitir a contagem de não lidas
    for (const participant of participants) {
      const userId = participant.userId || participant.clientId;
      
      // Pular o usuário excluído (geralmente quem enviou a mensagem)
      if (excludeUserId && userId === excludeUserId) {
        console.log(`[emitUnreadCountUpdate] Pulando remetente: ${userId}`);
        continue;
      }

      try {
        console.log(`[emitUnreadCountUpdate] Calculando mensagens não lidas para: ${userId}`);
        const unreadData = await messageService.getUnreadMessages(userId);
        console.log(`[emitUnreadCountUpdate] Usuário ${userId} tem ${unreadData.totalUnread} mensagens não lidas`);
        
        // Emitir para sockets específicos do usuário
        const userSockets = Array.from(io.sockets.sockets.values())
          .filter(socket => socket.user && (socket.user.id === userId));
        
        console.log(`[emitUnreadCountUpdate] Encontrados ${userSockets.length} sockets para usuário ${userId}`);
        
        userSockets.forEach(socket => {
          try {
            socket.emit('unread:update', unreadData);
            console.log(`[emitUnreadCountUpdate] Enviado unread:update para socket do usuário ${userId}`);
          } catch (emitError) {
            console.error(`[emitUnreadCountUpdate] Erro ao emitir para socket ${socket.id}:`, emitError);
          }
        });
      } catch (error) {
        console.error(`Error getting unread count for user ${userId}:`, error);
      }
    }
  } catch (error) {
    console.error('Error emitting unread count update:', error);
  }
};

module.exports = {
  register
};
