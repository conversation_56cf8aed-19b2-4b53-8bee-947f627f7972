{"permissions": {"allow": ["<PERSON><PERSON>(tail:*)", "Bash(find:*)", "Bash(npx prisma:*)", "Bash(docker compose exec:*)", "Bash(docker compose restart:*)", "Bash(docker compose logs:*)", "Bash(grep:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(docker compose:*)", "<PERSON><PERSON>(sed:*)", "Bash(wc:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(node:*)", "Read(c:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\components\\users/**)", "Read(c:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\components\\users/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\app\\modules\\admin\\users/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\components\\ui/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\app\\modules\\admin\\users/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\app\\modules\\admin\\users/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\components\\ui/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\app\\dashboard/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\components\\dashboard\\Sidebar/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\hooks\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\hooks\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\components\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\components\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\components\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\components\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\components\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\components\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\components\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-frontend\\src\\components\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\services\\chat\\modules/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\controllers\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\services\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\services\\chat\\modules/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\controllers\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\controllers\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\controllers\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\services\\chat/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\services\\chat\\modules/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\services\\chat\\modules/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\services\\chat\\modules/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\services\\chat\\modules/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\services\\chat\\modules/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\services\\chat\\modules/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\services\\chat\\modules/**)", "Read(/C:\\Program Files (x86)\\High Tide\\high-tide-systems-backend\\src\\services\\chat\\modules/**)"], "deny": []}}