"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/hooks/chat/useSocketEvents.js":
/*!*******************************************!*\
  !*** ./src/hooks/chat/useSocketEvents.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSocketEvents: () => (/* binding */ useSocketEvents)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\nvar _s = $RefreshSig$();\n\n\nconst useSocketEvents = (socket, isConnected, param)=>{\n    let { addMessage, setUnreadCount, loadUnreadCount, loadConversations, updateConversationLastMessage } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    // Configurar eventos do socket\n    const setupSocketEvents = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSocketEvents.useCallback[setupSocketEvents]\": ()=>{\n            if (!socket || !isConnected) return;\n            console.log('Configurando eventos do WebSocket...');\n            // Evento: Nova mensagem recebida\n            const handleNewMessage = {\n                \"useSocketEvents.useCallback[setupSocketEvents].handleNewMessage\": (data)=>{\n                    console.log('Nova mensagem recebida via WebSocket:', data);\n                    if (data === null || data === void 0 ? void 0 : data.message) {\n                        // Adicionar mensagem ao estado\n                        addMessage(data.message);\n                        // Atualizar a última mensagem da conversa\n                        if (updateConversationLastMessage && data.message.conversationId) {\n                            updateConversationLastMessage(data.message.conversationId, data.message);\n                        }\n                        // Atualizar contagem de não lidas se a mensagem não for do usuário atual\n                        const isOwnMessage = data.message.senderId === (user === null || user === void 0 ? void 0 : user.id) || data.message.senderClientId === (user === null || user === void 0 ? void 0 : user.id);\n                        if (!isOwnMessage) {\n                            loadUnreadCount(true);\n                        }\n                        // Disparar evento customizado para forçar atualização da UI\n                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {\n                            detail: {\n                                type: 'message',\n                                action: 'new',\n                                conversationId: data.message.conversationId,\n                                timestamp: Date.now()\n                            }\n                        }));\n                    }\n                }\n            }[\"useSocketEvents.useCallback[setupSocketEvents].handleNewMessage\"];\n            // Evento: Atualização de mensagens não lidas\n            const handleUnreadUpdate = {\n                \"useSocketEvents.useCallback[setupSocketEvents].handleUnreadUpdate\": (data)=>{\n                    console.log('Atualização de contagem não lidas via WebSocket:', data);\n                    // Verificar diferentes formatos de dados que podem vir do backend\n                    let unreadCount = 0;\n                    if (typeof (data === null || data === void 0 ? void 0 : data.count) === 'number') {\n                        unreadCount = data.count;\n                    } else if (typeof (data === null || data === void 0 ? void 0 : data.unreadCount) === 'number') {\n                        unreadCount = data.unreadCount;\n                    } else if (typeof (data === null || data === void 0 ? void 0 : data.totalUnread) === 'number') {\n                        unreadCount = data.totalUnread;\n                    } else if (typeof data === 'number') {\n                        unreadCount = data;\n                    }\n                    console.log(\"[WebSocket] Atualizando contador de mensagens n\\xe3o lidas para: \".concat(unreadCount));\n                    setUnreadCount(unreadCount);\n                }\n            }[\"useSocketEvents.useCallback[setupSocketEvents].handleUnreadUpdate\"];\n            // Evento: Conversas atualizadas\n            const handleConversationsUpdate = {\n                \"useSocketEvents.useCallback[setupSocketEvents].handleConversationsUpdate\": (data)=>{\n                    console.log('Conversas atualizadas via WebSocket:', data);\n                    // Recarregar conversas sem usar cache\n                    loadConversations(true);\n                }\n            }[\"useSocketEvents.useCallback[setupSocketEvents].handleConversationsUpdate\"];\n            // Evento: Nova conversa criada\n            const handleConversationCreated = {\n                \"useSocketEvents.useCallback[setupSocketEvents].handleConversationCreated\": (data)=>{\n                    console.log('Nova conversa criada via WebSocket:', data);\n                    // Recarregar conversas para incluir a nova\n                    loadConversations(true);\n                }\n            }[\"useSocketEvents.useCallback[setupSocketEvents].handleConversationCreated\"];\n            // Evento: Participante removido\n            const handleParticipantRemoved = {\n                \"useSocketEvents.useCallback[setupSocketEvents].handleParticipantRemoved\": (data)=>{\n                    console.log('Participante removido via WebSocket:', data);\n                    // Se o usuário atual foi removido, recarregar conversas\n                    if ((data === null || data === void 0 ? void 0 : data.userId) === (user === null || user === void 0 ? void 0 : user.id) || (data === null || data === void 0 ? void 0 : data.clientId) === (user === null || user === void 0 ? void 0 : user.id)) {\n                        console.log('Usuário atual removido de uma conversa, recarregando...');\n                        loadConversations(true);\n                    }\n                }\n            }[\"useSocketEvents.useCallback[setupSocketEvents].handleParticipantRemoved\"];\n            // Registrar eventos\n            socket.on('message:new', handleNewMessage);\n            socket.on('unread:update', handleUnreadUpdate);\n            socket.on('conversations:update', handleConversationsUpdate);\n            socket.on('conversation:created', handleConversationCreated);\n            socket.on('participant:removed', handleParticipantRemoved);\n            // Cleanup function\n            return ({\n                \"useSocketEvents.useCallback[setupSocketEvents]\": ()=>{\n                    socket.off('message:new', handleNewMessage);\n                    socket.off('unread:update', handleUnreadUpdate);\n                    socket.off('conversations:update', handleConversationsUpdate);\n                    socket.off('conversation:created', handleConversationCreated);\n                    socket.off('participant:removed', handleParticipantRemoved);\n                }\n            })[\"useSocketEvents.useCallback[setupSocketEvents]\"];\n        }\n    }[\"useSocketEvents.useCallback[setupSocketEvents]\"], [\n        socket,\n        isConnected,\n        addMessage,\n        setUnreadCount,\n        loadUnreadCount,\n        loadConversations,\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    // Configurar eventos quando socket conectar\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSocketEvents.useEffect\": ()=>{\n            if (socket && isConnected) {\n                const cleanup = setupSocketEvents();\n                return cleanup;\n            }\n        }\n    }[\"useSocketEvents.useEffect\"], [\n        socket,\n        isConnected,\n        setupSocketEvents\n    ]);\n    // Emitir evento para entrar em sala de conversa\n    const joinConversationRoom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSocketEvents.useCallback[joinConversationRoom]\": (conversationId)=>{\n            if ((socket === null || socket === void 0 ? void 0 : socket.connected) && conversationId) {\n                console.log(\"Entrando na sala da conversa \".concat(conversationId));\n                socket.emit('join:conversation', {\n                    conversationId\n                });\n            }\n        }\n    }[\"useSocketEvents.useCallback[joinConversationRoom]\"], [\n        socket\n    ]);\n    // Emitir evento para sair de sala de conversa\n    const leaveConversationRoom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSocketEvents.useCallback[leaveConversationRoom]\": (conversationId)=>{\n            if ((socket === null || socket === void 0 ? void 0 : socket.connected) && conversationId) {\n                console.log(\"Saindo da sala da conversa \".concat(conversationId));\n                socket.emit('leave:conversation', {\n                    conversationId\n                });\n            }\n        }\n    }[\"useSocketEvents.useCallback[leaveConversationRoom]\"], [\n        socket\n    ]);\n    // Emitir evento de digitação\n    const emitTyping = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSocketEvents.useCallback[emitTyping]\": function(conversationId) {\n            let isTyping = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n            if ((socket === null || socket === void 0 ? void 0 : socket.connected) && conversationId) {\n                socket.emit('user:typing', {\n                    conversationId,\n                    isTyping,\n                    userId: user === null || user === void 0 ? void 0 : user.id\n                });\n            }\n        }\n    }[\"useSocketEvents.useCallback[emitTyping]\"], [\n        socket,\n        user === null || user === void 0 ? void 0 : user.id\n    ]);\n    return {\n        joinConversationRoom,\n        leaveConversationRoom,\n        emitTyping,\n        setupSocketEvents\n    };\n};\n_s(useSocketEvents, \"YpP+4KV+mhO55S/xOdxemgA7+ck=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth\n    ];\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/chat/useSocketEvents.js\n"));

/***/ })

});