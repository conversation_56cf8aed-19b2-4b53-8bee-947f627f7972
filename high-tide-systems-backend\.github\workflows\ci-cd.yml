  name: Backend CI/CD

  on:
    push:
      branches: [ main, develop ]
    pull_request:
      branches: [ main ]

  env:
    REGISTRY: ghcr.io
    IMAGE_NAME: ${{ github.repository }}

  jobs:
    # Testes do Backend
    test:
      runs-on: ubuntu-latest
      services:
        postgres:
          image: postgres:17
          env:
            POSTGRES_PASSWORD: test
            POSTGRES_DB: test_db
            POSTGRES_USER: test
          options: >-
            --health-cmd pg_isready
            --health-interval 10s
            --health-timeout 5s
            --health-retries 5
          ports:
            - 5432:5432

        redis:
          image: redis:7.4.3
          options: >-
            --health-cmd "redis-cli ping"
            --health-interval 10s
            --health-timeout 5s
            --health-retries 5
          ports:
            - 6379:6379

      steps:
        - name: Checkout código
          uses: actions/checkout@v4

        - name: Setup Node.js
          uses: actions/setup-node@v4
          with:
            node-version: '18'  # Compatível com Dockerfile
            cache: 'npm'

        - name: Instalar dependências
          run: npm ci

        - name: Executar testes
          run: npm test --if-present
          env:
            DATABASE_URL: postgresql://test:test@localhost:5432/test_db
            REDIS_URL: redis://localhost:6379
            NODE_ENV: test

    # Build da imagem Docker
    build:
      needs: [test]
      runs-on: ubuntu-latest
      if: github.ref == 'refs/heads/main'

      steps:
        - name: Checkout código
          uses: actions/checkout@v4

        - name: Setup Docker Buildx
          uses: docker/setup-buildx-action@v3

        - name: Login no GitHub Container Registry
          uses: docker/login-action@v3
          with:
            registry: ${{ env.REGISTRY }}
            username: ${{ github.actor }}
            password: ${{ secrets.GITHUB_TOKEN }}

        - name: Build e push Backend
          uses: docker/build-push-action@v5
          with:
            context: .
            push: true
            tags: |
              ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
              ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
            cache-from: type=gha
            cache-to: type=gha,mode=max

    # Deploy para VPS
    deploy:
      needs: [build]
      runs-on: ubuntu-latest
      if: github.ref == 'refs/heads/main'

      steps:
        - name: Deploy Backend para VPS
          uses: appleboy/ssh-action@v1.0.3
          with:
            host: ${{ secrets.VPS_HOST }}
            username: ${{ secrets.VPS_USERNAME }}
            key: ${{ secrets.VPS_SSH_KEY }}
            port: 22
            script: |
              set -e

              echo "🚀 Iniciando deploy do Backend..."

              # Ficar na raiz onde está o docker-compose.yml principal
              cd /root

              # Criar backup do banco antes do deploy
              echo "🗄️ Criando backup do banco..."
              if [ -f "scripts/backup-db.sh" ]; then
                chmod +x scripts/backup-db.sh
                ./scripts/backup-db.sh
              else
                echo "⚠️ Script de backup não encontrado, continuando sem backup..."
              fi

              # Atualizar código do backend
              echo "📥 Atualizando código do backend..."
              cd high-tide-systems-backend
              git pull origin main
              cd /root

              # Login no registry do GitHub
              echo "🔐 Login no GitHub Container Registry..."
              echo ${{ secrets.GITHUB_TOKEN }} | docker login ghcr.io -u ${{ github.actor }} --password-stdin

              # Pull da nova imagem
              echo "📦 Baixando nova imagem do backend..."
              docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}

              # Criar backup da configuração atual
              cp docker-compose.yml docker-compose.yml.backup

              # Substituir build por image no docker-compose.yml
              echo "🔧 Atualizando configuração do docker-compose..."
              sed -i '/api:/,/environment:/ {
                /build:/,/dockerfile: Dockerfile/c\    image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
              }' docker-compose.yml

              # Parar e remover container existente do backend
              echo "🛑 Parando containers do backend..."
              docker compose stop api studio || true
              docker compose rm -f api studio || true

              # Verificar e liberar portas
              echo "🔍 Verificando portas..."
              for port in 5000 5555; do
                if lsof -ti:$port; then
                  echo "⚠️ Porta $port em uso, liberando..."
                  lsof -ti:$port | xargs kill -9 || true
                  sleep 2
                fi
              done

              # Atualizar apenas os serviços do backend (rolling update)
              echo "🔄 Iniciando novos containers do backend..."
              docker compose up -d --no-deps api studio

              # Aguardar containers ficarem prontos
              echo "⏳ Aguardando backend inicializar..."
              sleep 45

              # Verificar se containers estão rodando
              echo "🔍 Verificando se containers estão ativos..."
              if ! docker compose ps api | grep -q "Up"; then
                echo "❌ Container API não está rodando"
                docker compose logs api
                exit 1
              fi

              if ! docker compose ps studio | grep -q "Up"; then
                echo "⚠️ Container Studio não está rodando"
                docker compose logs studio
              fi

              # Executar migrações do banco se necessário
              echo "🗃️ Verificando migrações do banco..."
              if docker compose exec -T api npx prisma migrate status | grep -q "Following migration"; then
                echo "📊 Executando migrações do banco..."
                docker compose exec -T api npx prisma migrate deploy
                echo "✅ Migrações aplicadas com sucesso"
              else
                echo "✅ Nenhuma migração pendente"
              fi

              # Health check robusto
              echo "🩺 Verificando saúde do backend..."
              max_attempts=12
              attempt=1

              while [ $attempt -le $max_attempts ]; do
                if docker compose exec -T api netstat -tlnp | grep -q ":5000"; then
                  echo "✅ Backend está escutando na porta 5000"

                  # Health check externo (do host, onde curl está disponível)
                  if curl -f -m 10 "http://127.0.0.1:5000/health/simple" > /dev/null 2>&1; then
                    echo "✅ Health check passou!"
                    break
                  fi
                fi

                echo "⏳ Tentativa $attempt/$max_attempts - Backend ainda não está pronto..."
                if [ $attempt -eq $max_attempts ]; then
                  echo "❌ Backend não respondeu após $max_attempts tentativas"
                  echo "📋 Logs recentes do container:"
                  docker compose logs --tail 30 api
                  echo "📊 Status do container:"
                  docker compose ps api
                  exit 1
                fi
                sleep 15
                attempt=$((attempt + 1))
              done

              # Verificar Prisma Studio
              if docker compose exec -T studio curl -f -m 5 http://localhost:5555 > /dev/null 2>&1; then
                echo "✅ Prisma Studio está acessível!"
              else
                echo "⚠️ Prisma Studio pode não estar respondendo"
              fi

              # Limpar imagens antigas
              echo "🧹 Limpando imagens antigas..."
              docker image prune -f

              # Status final
              echo "📊 Status dos containers:"
              docker compose ps

              echo "✅ Deploy do backend concluído com sucesso!"

        - name: Notificar Discord
          if: always()
          uses: Ilshidur/action-discord@master
          env:
            DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
          with:
            args: |
              ⚙️ **Backend Deploy** 
              **Status:** ${{ job.status == 'success' && '✅ Sucesso' || '❌ Falhou' }}
              **Branch:** ${{ github.ref_name }}
              **Commit:** ${{ github.event.head_commit.message }}
              **Autor:** ${{ github.event.head_commit.author.name }}
              **API:** https://hightide.site/api
              **Studio:** https://studio.hightide.site