'use client';

import React, { createContext, useContext, useEffect, useMemo } from 'react';
import { useAuth } from './AuthContext';

// Import custom hooks
import { useSocketConnection } from '../hooks/chat/useSocketConnection';
import { useCache } from '../hooks/chat/useCache';
import { useConversations } from '../hooks/chat/useConversations';
import { useMessages } from '../hooks/chat/useMessages';
import { useChatUI as useChatUIHook } from '../hooks/chat/useChatUI';
import { useSocketEvents } from '../hooks/chat/useSocketEvents';

const ChatContext = createContext(null);

export const ChatProvider = ({ children }) => {
  const { user } = useAuth();
  
  // Initialize hooks
  const { socket, isConnected, handleAuthError } = useSocketConnection();
  const { clearCacheForUser } = useCache();
  const { 
    conversations, 
    activeConversation, 
    setActiveConversation,
    loadConversations,
    createConversation,
    createOrGetConversation,
    addParticipantToGroup,
    addMultipleParticipantsToGroup,
    removeParticipantFromGroup,
    cleanupLeftConversations,
    updateConversationLastMessage
  } = useConversations(socket, handleAuthError);
  
  const {
    messages,
    unreadCount,
    loadMessages,
    sendMessage,
    loadUnreadCount,
    markMessagesAsRead,
    resetUnreadCount,
    deleteMessages,
    addMessage
  } = useMessages(socket, handleAuthError);
  
  const {
    isPanelOpen,
    isModalOpen,
    isLoading,
    toggleChatPanel,
    toggleChatModal,
    setIsPanelOpen,
    setIsModalOpen,
    setIsLoading
  } = useChatUIHook();

  // Setup socket events
  const { joinConversationRoom, leaveConversationRoom, emitTyping } = useSocketEvents(
    socket,
    isConnected,
    {
      addMessage,
      setUnreadCount: (count) => {
        // Update unread count from socket events
      },
      loadUnreadCount,
      loadConversations,
      updateConversationLastMessage
    }
  );

  // Limpar estado do chat quando o usuário muda
  useEffect(() => {
    if (user) {
      console.log('Usuário logado, limpando estado do chat para novo usuário:', user.fullName);
      clearCacheForUser();
    }
  }, [user?.id, clearCacheForUser]);

  // Carregar dados iniciais quando conectado
  useEffect(() => {
    if (user && socket && isConnected) {
      console.log('Socket conectado, carregando dados iniciais do chat...');
      loadConversations();
      loadUnreadCount();
    }
  }, [user, socket, isConnected, loadConversations, loadUnreadCount]);

  // Context value
  const contextValue = useMemo(() => ({
    // Connection state
    socket,
    isConnected,
    
    // Data
    conversations,
    messages,
    unreadCount,
    activeConversation,
    
    // UI state
    isPanelOpen,
    isModalOpen,
    isLoading,
    
    // Conversation actions
    loadConversations,
    createConversation,
    createOrGetConversation,
    setActiveConversation,
    addParticipantToGroup,
    addMultipleParticipantsToGroup,
    removeParticipantFromGroup,
    cleanupLeftConversations,
    
    // Message actions
    loadMessages,
    sendMessage,
    loadUnreadCount,
    markMessagesAsRead,
    resetUnreadCount,
    deleteMessages,
    
    // UI actions
    toggleChatPanel,
    toggleChatModal,
    setIsPanelOpen,
    setIsModalOpen,
    setIsLoading,
    
    // Socket events
    joinConversationRoom,
    leaveConversationRoom,
    emitTyping
  }), [
    socket,
    isConnected,
    conversations,
    messages,
    unreadCount,
    activeConversation,
    isPanelOpen,
    isModalOpen,
    isLoading,
    loadConversations,
    createConversation,
    createOrGetConversation,
    setActiveConversation,
    addParticipantToGroup,
    addMultipleParticipantsToGroup,
    removeParticipantFromGroup,
    cleanupLeftConversations,
    loadMessages,
    sendMessage,
    loadUnreadCount,
    markMessagesAsRead,
    resetUnreadCount,
    deleteMessages,
    toggleChatPanel,
    toggleChatModal,
    setIsPanelOpen,
    setIsModalOpen,
    setIsLoading,
    joinConversationRoom,
    leaveConversationRoom,
    emitTyping
  ]);

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
};

// Hook para usar o contexto
export const useChat = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};

// Hook específico para dados do chat (conversas e mensagens)
export const useChatData = () => {
  const { conversations, messages, unreadCount, activeConversation } = useChat();
  return { conversations, messages, unreadCount, activeConversation };
};

// Hook específico para ações do chat
export const useChatActions = () => {
  const {
    loadConversations,
    createConversation,
    createOrGetConversation,
    setActiveConversation,
    addParticipantToGroup,
    addMultipleParticipantsToGroup,
    removeParticipantFromGroup,
    loadMessages,
    sendMessage,
    loadUnreadCount,
    markMessagesAsRead,
    resetUnreadCount,
    deleteMessages,
  } = useChat();
  
  return {
    loadConversations,
    createConversation,
    createOrGetConversation,
    setActiveConversation,
    addParticipantToGroup,
    addMultipleParticipantsToGroup,
    removeParticipantFromGroup,
    loadMessages,
    sendMessage,
    loadUnreadCount,
    markMessagesAsRead,
    resetUnreadCount,
    deleteMessages,
  };
};

// Hook específico para UI do chat
export const useChatUI = () => {
  const {
    isPanelOpen,
    isModalOpen,
    isLoading,
    toggleChatPanel,
    toggleChatModal,
    setIsPanelOpen,
    setIsModalOpen,
    setIsLoading
  } = useChat();
  
  return {
    isPanelOpen,
    isModalOpen,
    isLoading,
    toggleChatPanel,
    toggleChatModal,
    setIsPanelOpen,
    setIsModalOpen,
    setIsLoading
  };
};

// Hook específico para conexão socket
export const useChatSocket = () => {
  const {
    socket,
    isConnected,
    joinConversationRoom,
    leaveConversationRoom,
    emitTyping
  } = useChat();
  
  return {
    socket,
    isConnected,
    joinConversationRoom,
    leaveConversationRoom,
    emitTyping
  };
};