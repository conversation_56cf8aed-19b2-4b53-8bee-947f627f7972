// src/services/chat/modules/conversationQueryService.js
const prisma = require('../../../utils/prisma');
const { getUserData, clientSelectWithPerson } = require('./validationService');

/**
 * Busca conversas do usuário com query funcional
 * Versão simplificada que funciona corretamente (temporária)
 * @param {string} userId - ID do usuário
 * @param {Object} filters - Filtros opcionais
 * @returns {Promise<Array>} - Lista de conversas
 */
const getUserConversations = async (userId, filters = {}) => {
  try {
    const userData = await getUserData(userId);
    
    if (!userData) {
      console.log('Usuário não encontrado');
      return [];
    }

    console.log(`Buscando conversas para usuário ${userId} (${userData.type})`);

    // Buscar conversas usando Prisma ORM (funcional e simples)
    const conversations = await prisma.conversation.findMany({
      where: {
        isActive: true,
        participants: {
          some: {
            ...(userData.type === 'CLIENT' ? { clientId: userId } : { userId: userId }),
            leftAt: null
          }
        }
      },
      include: {
        participants: {
          where: { leftAt: null },
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                role: true
              }
            },
            client: {
              select: clientSelectWithPerson
            }
          }
        },
        messages: {
          where: { isDeleted: false },
          orderBy: { createdAt: 'desc' },
          take: 1,
          include: {
            sender: {
              select: {
                id: true,
                fullName: true,
                email: true,
                role: true
              }
            },
            senderClient: {
              select: clientSelectWithPerson
            }
          }
        }
      },
      orderBy: [
        { lastMessageAt: { sort: 'desc', nulls: 'last' } },
        { createdAt: 'desc' }
      ]
    });

    // Adicionar contagem simples de mensagens não lidas e última mensagem para cada conversa
    const conversationsWithUnread = await Promise.all(
      conversations.map(async (conversation) => {
        try {
          // Encontrar o participante atual
          const currentParticipant = conversation.participants.find(p => 
            (userData.type === 'CLIENT' ? p.clientId : p.userId) === userId
          );

          if (!currentParticipant) {
            return { 
              ...conversation, 
              unreadCount: 0,
              lastMessage: conversation.messages?.[0] || null
            };
          }

          // Contar mensagens não lidas de forma simplificada
          const unreadCount = await prisma.message.count({
            where: {
              conversationId: conversation.id,
              isDeleted: false,
              // Não contar mensagens próprias
              AND: [
                { senderId: { not: userData.type === 'CLIENT' ? null : userId } },
                { senderClientId: { not: userData.type === 'CLIENT' ? userId : null } }
              ],
              // Mensagens que não têm status de leitura ou não estão marcadas como lidas
              OR: [
                {
                  statuses: {
                    none: {
                      participantId: currentParticipant.id,
                      status: 'READ'
                    }
                  }
                }
              ]
            }
          });

          return { 
            ...conversation, 
            unreadCount,
            lastMessage: conversation.messages?.[0] || null
          };
        } catch (error) {
          console.warn(`Erro ao contar mensagens não lidas para conversa ${conversation.id}:`, error.message);
          return { 
            ...conversation, 
            unreadCount: 0,
            lastMessage: conversation.messages?.[0] || null
          };
        }
      })
    );

    // Remover o array de messages do resultado final (já extraímos a lastMessage)
    const finalConversations = conversationsWithUnread.map(conv => {
      const { messages, ...conversationWithoutMessages } = conv;
      return conversationWithoutMessages;
    });

    console.log(`${finalConversations.length} conversas encontradas para usuário ${userId}`);
    return finalConversations;
    
  } catch (error) {
    console.error('Erro ao buscar conversas do usuário:', error);
    throw error;
  }
};

/**
 * Busca conversas com filtros avançados (versão simplificada)
 * @param {string} userId - ID do usuário
 * @param {Object} options - Opções de busca
 * @returns {Promise<Object>} - Resultado paginado
 */
const getUserConversationsWithPagination = async (userId, options = {}) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      search, 
      type
    } = options;

    const userData = await getUserData(userId);
    if (!userData) {
      return { conversations: [], total: 0, page, limit };
    }

    const skip = (page - 1) * limit;

    // Construir filtros
    const whereConditions = {
      isActive: true,
      participants: {
        some: {
          ...(userData.type === 'CLIENT' ? { clientId: userId } : { userId: userId }),
          leftAt: null
        }
      },
      ...(search && { title: { contains: search, mode: 'insensitive' } }),
      ...(type && { type })
    };

    // Contagem total
    const total = await prisma.conversation.count({
      where: whereConditions
    });

    // Buscar conversas com paginação
    const conversations = await prisma.conversation.findMany({
      where: whereConditions,
      include: {
        participants: {
          where: { leftAt: null },
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                role: true
              }
            },
            client: {
              select: clientSelectWithPerson
            }
          }
        }
      },
      orderBy: [
        { lastMessageAt: { sort: 'desc', nulls: 'last' } },
        { createdAt: 'desc' }
      ],
      skip,
      take: limit
    });

    // Adicionar unreadCount simples
    const result = conversations.map(conv => ({ ...conv, unreadCount: 0 }));

    return {
      conversations: result,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };

  } catch (error) {
    console.error('Erro ao buscar conversas paginadas:', error);
    throw error;
  }
};

/**
 * Busca conversas com mensagens não lidas (versão simplificada)
 * @param {string} userId - ID do usuário
 * @returns {Promise<Array>} - Conversas com mensagens não lidas
 */
const getConversationsWithUnreadMessages = async (userId) => {
  try {
    const userData = await getUserData(userId);
    if (!userData) return [];

    // Implementação simplificada que retorna todas as conversas ativas
    // TODO: Otimizar para filtrar apenas conversas com mensagens não lidas
    const conversations = await getUserConversations(userId);
    
    // Filtrar apenas conversas com mensagens não lidas
    return conversations.filter(conv => conv.unreadCount > 0);
  } catch (error) {
    console.error('Erro ao buscar conversas com mensagens não lidas:', error);
    throw error;
  }
};

module.exports = {
  getUserConversations,
  getUserConversationsWithPagination,
  getConversationsWithUnreadMessages
};