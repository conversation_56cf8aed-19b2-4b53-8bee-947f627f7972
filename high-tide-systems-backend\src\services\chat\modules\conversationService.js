// src/services/chat/modules/conversationService.js
const prisma = require('../../../utils/prisma');
const { NotFoundError, ForbiddenError } = require('../../../utils/errors');
const { 
  getUserData, 
  validateParticipantsCompany,
  validateConversationAccess,
  validateConversationModifyAccess,
  clientSelectWithPerson 
} = require('./validationService');

/**
 * Cria uma nova conversa
 * @param {Object} data - Dados da conversa
 * @param {string} userId - ID do usuário que está criando a conversa
 * @returns {Promise<Object>} - Conversa criada
 */
const createConversation = async (data, userId) => {
  try {
    const creator = await getUserData(userId);
    
    if (!creator) {
      throw new NotFoundError('Criador da conversa não encontrado');
    }

    const { participants, title, type = 'GROUP' } = data;

    if (!participants || participants.length === 0) {
      throw new Error('É necessário pelo menos um participante além do criador');
    }

    // Extrair IDs dos participantes
    const participantIds = participants.map(p => p.userId || p.clientId).filter(Boolean);

    // Validar se todos os participantes são da mesma empresa
    await validateParticipantsCompany(participantIds, creator.companyId, creator.role);

    // Criar conversa
    const conversation = await prisma.conversation.create({
      data: {
        title,
        type,
        companyId: creator.companyId,
        branchId: creator.branchId,
        createdById: userId,
        participants: {
          create: [
            // Adicionar criador como participante admin
            {
              [creator.type === 'CLIENT' ? 'clientId' : 'userId']: userId,
              isAdmin: true
            },
            // Adicionar outros participantes
            ...participants.map(p => ({
              userId: p.userId || undefined,
              clientId: p.clientId || undefined,
              isAdmin: p.isAdmin || false
            }))
          ]
        }
      },
      include: {
        participants: {
          where: { leftAt: null },
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                role: true
              }
            },
            client: {
              select: clientSelectWithPerson
            }
          }
        }
      }
    });

    console.log(`Nova conversa criada: ${conversation.id}`);
    return conversation;
  } catch (error) {
    console.error('Erro ao criar conversa:', error);
    throw error;
  }
};

/**
 * Busca ou cria uma conversa individual entre dois usuários
 * @param {string} userId1 - ID do primeiro usuário
 * @param {string} userId2 - ID do segundo usuário
 * @returns {Promise<Object>} - Conversa encontrada ou criada
 */
const findOrCreateIndividualConversation = async (userId1, userId2) => {
  try {
    // Buscar conversa existente
    const existingConversation = await findIndividualConversation(userId1, userId2);
    
    if (existingConversation) {
      return existingConversation;
    }

    // Criar nova conversa individual
    return await createConversation({
      participants: [{ userId: userId2 }],
      type: 'INDIVIDUAL'
    }, userId1);
  } catch (error) {
    console.error('Erro ao encontrar/criar conversa individual:', error);
    throw error;
  }
};

/**
 * Busca uma conversa individual existente entre dois usuários
 * @param {string} userId1 - ID do primeiro usuário
 * @param {string} userId2 - ID do segundo usuário
 * @returns {Promise<Object|null>} - Conversa encontrada ou null
 */
const findIndividualConversation = async (userId1, userId2) => {
  try {
    // Buscar conversas individuais onde ambos os usuários são participantes
    const conversation = await prisma.conversation.findFirst({
      where: {
        type: 'INDIVIDUAL',
        AND: [
          // Primeiro usuário deve ser participante
          {
            participants: {
              some: {
                OR: [
                  { userId: userId1 },
                  { clientId: userId1 }
                ],
                leftAt: null
              }
            }
          },
          // Segundo usuário deve ser participante
          {
            participants: {
              some: {
                OR: [
                  { userId: userId2 },
                  { clientId: userId2 }
                ],
                leftAt: null
              }
            }
          }
        ]
      },
      include: {
        participants: {
          where: { leftAt: null },
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                role: true
              }
            },
            client: {
              select: clientSelectWithPerson
            }
          }
        }
      }
    });

    return conversation;
  } catch (error) {
    console.error('Erro ao buscar conversa individual:', error);
    throw error;
  }
};

/**
 * Busca uma conversa por ID
 * @param {string} conversationId - ID da conversa
 * @param {string} userId - ID do usuário solicitante
 * @returns {Promise<Object>} - Dados da conversa
 */
const getConversationById = async (conversationId, userId) => {
  try {
    // Validar acesso
    await validateConversationAccess(conversationId, userId);

    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        participants: {
          where: { leftAt: null },
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                role: true
              }
            },
            client: {
              select: clientSelectWithPerson
            }
          }
        }
      }
    });

    if (!conversation) {
      throw new NotFoundError('Conversa não encontrada');
    }

    return conversation;
  } catch (error) {
    console.error('Erro ao buscar conversa:', error);
    throw error;
  }
};

/**
 * Atualiza dados de uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {Object} data - Novos dados
 * @param {string} userId - ID do usuário que está atualizando
 * @returns {Promise<Object>} - Conversa atualizada
 */
const updateConversation = async (conversationId, data, userId) => {
  try {
    // Validar permissões de modificação
    await validateConversationModifyAccess(conversationId, userId);

    const { title } = data;

    const conversation = await prisma.conversation.update({
      where: { id: conversationId },
      data: { title },
      include: {
        participants: {
          where: { leftAt: null },
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                role: true
              }
            },
            client: {
              select: clientSelectWithPerson
            }
          }
        }
      }
    });

    console.log(`Conversa ${conversationId} atualizada`);
    return conversation;
  } catch (error) {
    console.error('Erro ao atualizar conversa:', error);
    throw error;
  }
};

/**
 * Atualiza o timestamp da última mensagem de uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {string} messageId - ID da mensagem
 * @returns {Promise<void>}
 */
const updateLastMessage = async (conversationId, messageId) => {
  try {
    await prisma.conversation.update({
      where: { id: conversationId },
      data: { lastMessageAt: new Date() }
    });
  } catch (error) {
    console.error('Erro ao atualizar última mensagem:', error);
    throw error;
  }
};

module.exports = {
  createConversation,
  findOrCreateIndividualConversation,
  findIndividualConversation,
  getConversationById,
  updateConversation,
  updateLastMessage
};