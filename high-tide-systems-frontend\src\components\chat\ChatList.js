'use client';

import React, { useMemo, useCallback, useState, useEffect } from 'react';
import { useChat } from '@/contexts/ChatContext';
import { formatDistanceToNow, format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useAuth } from '@/contexts/AuthContext';
import { Users, MessageCircle, Search, Plus, MoreVertical, Clock, Check, CheckCheck } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const ChatList = ({ searchQuery = '' }) => {
  const { conversations, setActiveConversation, messages, loadConversations, isLoading } = useChat();
  const { user } = useAuth();

  // Estado para forçar re-renderização
  const [updateTrigger, setUpdateTrigger] = useState(0);

  // Carregar conversas quando o componente é montado
  useEffect(() => {
    // Carregar conversas inicialmente
    console.log('ChatList montado, verificando se é necessário carregar conversas...');
    if (user && user.id) {
      console.log('Usuário logado:', user.id);
      // Carregar conversas inicialmente
      console.log('Carregando conversas iniciais');
      loadConversations(false); // Usar cache se disponível na primeira carga
    } else {
      console.log('Usuário não logado, não carregando conversas');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.id]); // Executar quando o usuário mudar

  // Adicionar listener para eventos de atualização do WebSocket
  useEffect(() => {
    // Função para lidar com eventos de atualização
    const handleWebSocketUpdate = (event) => {
      const { type, action, conversationId } = event.detail;

      console.log(`ChatList recebeu evento de atualização: ${type}`, event.detail);

      // Forçar re-renderização para qualquer tipo de evento
      setUpdateTrigger(prev => prev + 1);

      // Se for uma nova mensagem, apenas forçar re-renderização
      if (type === 'message' && action === 'new') {
        console.log(`ChatList: Nova mensagem na conversa ${conversationId}, atualizando interface`);
        // A lastMessage já foi atualizada pelo updateConversationLastMessage
        // Apenas forçar re-renderização
        setUpdateTrigger(prev => prev + 1);
        return;
      }

      // Se for uma atualização de conversas, verificar se precisamos recarregar
      if (type === 'conversations') {
        // Se for uma ação de exclusão, não precisamos recarregar as conversas
        // pois o estado já foi atualizado no ChatContext
        if (action === 'delete') {
          console.log(`ChatList: Conversa ${conversationId} foi apagada, atualizando interface`);
          // Apenas forçar re-renderização
          setUpdateTrigger(prev => prev + 1);
        } else if (conversations.length === 0) {
          // Só recarregar se não tivermos conversas
          console.log('ChatList: Não há conversas carregadas, recarregando após evento de atualização');
          loadConversations(false).then(() => {
            console.log('ChatList: Conversas carregadas com sucesso');
            // Forçar outra re-renderização após o carregamento
            setUpdateTrigger(prev => prev + 1);
          });
        } else {
          console.log('ChatList: Já existem conversas carregadas, apenas atualizando interface');
          // Apenas forçar re-renderização
          setUpdateTrigger(prev => prev + 1);
        }
      }
    };

    // Adicionar listener
    window.addEventListener('chat:websocket:update', handleWebSocketUpdate);

    // Remover listener quando o componente for desmontado
    return () => {
      window.removeEventListener('chat:websocket:update', handleWebSocketUpdate);
    };
  }, [loadConversations, conversations.length]); // Dependências: loadConversations e conversations.length

  // Filtrar conversas com base na pesquisa
  const filteredConversations = useMemo(() => {
    // Log para debug
    console.log(`Recalculando filteredConversations. Trigger: ${updateTrigger}, Conversas: ${conversations.length}`);

    if (!searchQuery) return conversations;

    return conversations.filter(conversation => {
      // Para conversas individuais, pesquisar pelo nome do outro participante
      if (conversation.type === 'INDIVIDUAL') {
        const otherParticipant = conversation.participants?.find(
          p => (p.userId !== user?.id && p.clientId !== user?.id)
        );
        
        // Buscar pelo fullName do usuário ou cliente
        if (otherParticipant?.user?.fullName) {
          return otherParticipant.user.fullName.toLowerCase().includes(searchQuery.toLowerCase());
        }
        if (otherParticipant?.client) {
          const clientPersonName = otherParticipant.client.clientPersons?.[0]?.person?.fullName;
          let clientName;
          if (clientPersonName && clientPersonName.trim() !== '') {
            clientName = clientPersonName;
          } else {
            clientName = (otherParticipant.client.fullName && otherParticipant.client.fullName.trim() !== '') 
              ? otherParticipant.client.fullName 
              : otherParticipant.client.login;
          }
          return clientName.toLowerCase().includes(searchQuery.toLowerCase());
        }
        
        return false;
      }

      // Para grupos, pesquisar pelo título
      return conversation.title?.toLowerCase().includes(searchQuery.toLowerCase());
    });
  }, [conversations, searchQuery, user?.id, updateTrigger]); // Adicionado updateTrigger como dependência

  // Obter o nome da conversa - memoizado para evitar recalculos
  const getConversationName = useCallback((conversation) => {
    if (!conversation) return 'Conversa';

    if (conversation.type === 'GROUP') {
      return conversation.title || 'Grupo';
    }

    // Encontrar o outro participante (não o usuário atual)
    const otherParticipant = conversation.participants?.find(
      p => (p.userId !== user?.id && p.clientId !== user?.id)
    );

    // Retornar nome do usuário ou cliente
    if (otherParticipant?.user?.fullName) {
      return otherParticipant.user.fullName;
    }
    if (otherParticipant?.client) {
      const clientPersonName = otherParticipant.client.clientPersons?.[0]?.person?.fullName;
      if (clientPersonName && clientPersonName.trim() !== '') {
        return clientPersonName;
      }
      return (otherParticipant.client.fullName && otherParticipant.client.fullName.trim() !== '') 
        ? otherParticipant.client.fullName 
        : otherParticipant.client.login;
    }

    return 'Usuário';
  }, [user?.id]);

  // Obter a imagem da conversa - memoizado para evitar recalculos
  const getConversationImage = useCallback((conversation) => {
    if (!conversation) return null;

    if (conversation.type === 'GROUP') {
      return null; // Usar um ícone de grupo
    }

    // Encontrar o outro participante (não o usuário atual)
    const otherParticipant = conversation.participants?.find(
      p => (p.userId !== user?.id && p.clientId !== user?.id)
    );

    return otherParticipant?.user?.profileImageUrl;
  }, [user?.id]);

  // Formatar a data da última mensagem - memoizado para evitar recalculos
  const formatLastMessageTime = useCallback((timestamp) => {
    if (!timestamp) return '';

    try {
      return formatDistanceToNow(new Date(timestamp), {
        addSuffix: true,
        locale: ptBR
      });
    } catch (error) {
      console.error('Erro ao formatar data:', error);
      return '';
    }
  }, []);

  // Obter iniciais para avatar - memoizado para evitar recalculos
  const getInitials = useCallback((name) => {
    if (!name) return 'U';

    try {
      const names = name.split(' ');
      if (names.length === 1) return names[0].charAt(0);

      return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`;
    } catch (error) {
      console.error('Erro ao obter iniciais:', error);
      return 'U';
    }
  }, []);

  // Log para depuração - quando o componente é renderizado
  console.log(`ChatList renderizando. Trigger: ${updateTrigger}, Conversas: ${conversations.length}, Filtradas: ${filteredConversations.length}`);

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: {
        duration: 0.2
      }
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  if (filteredConversations.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6 text-gray-500 dark:text-gray-400 relative">
        {/* Indicador de status da conexão */}
        <div className="absolute top-4 right-4 flex flex-col items-end">
          <div className="flex items-center">
            {isLoading && (
                      <div className="flex items-center space-x-2 bg-blue-100 dark:bg-blue-900/30 px-3 py-1 rounded-full">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">Carregando...</span>
              </div>
            )}
          </div>
        </div>

        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
            <MessageCircle size={48} className="mx-auto mb-3 text-blue-500 dark:text-blue-400" />
          <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">
            {searchQuery ? 'Nenhuma conversa encontrada' : 'Nenhuma conversa iniciada'}
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {searchQuery 
              ? 'Tente ajustar sua pesquisa' 
              : 'Inicie uma conversa para começar a trocar mensagens'
            }
          </p>
        </motion.div>
      </div>
    );
  }

  return (
    <motion.div 
      className="relative"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Indicador de status da conexão */}
      <div className="absolute top-4 right-4 flex flex-col items-end z-10">
        <div className="flex items-center">
          {isLoading && (
                    <div className="flex items-center space-x-2 bg-blue-100 dark:bg-blue-900/30 px-3 py-1 rounded-full shadow-sm">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">Carregando...</span>
            </div>
          )}
        </div>
      </div>

      <div className="space-y-1 p-2">
        <AnimatePresence mode="wait">
          {filteredConversations.map((conversation, index) => {
            const name = getConversationName(conversation);
            const image = getConversationImage(conversation);
            // Usar a lastMessage que vem do backend
            const lastMessage = conversation.lastMessage;
            // Verificar se há mensagens não lidas (usar hasUnread do backend ou fallback para unreadCount)
            const hasUnread = conversation.hasUnread || conversation.unreadCount > 0;
            console.log(`[ChatList] Conversa ${conversation.id}: hasUnread=${hasUnread}, conversation.hasUnread=${conversation.hasUnread}, conversation.unreadCount=${conversation.unreadCount}`);
            return (
              <motion.button
                key={conversation.id}
                variants={itemVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                onClick={() => setActiveConversation(conversation.id)}
                className={`w-full flex items-center gap-4 p-4 transition-all duration-300 text-left rounded-2xl group relative overflow-hidden ${
                  hasUnread
                    ? 'bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/30 dark:to-blue-900/30 hover:from-cyan-100 hover:to-blue-100 dark:hover:from-cyan-800/40 dark:hover:to-blue-800/40 border-l-4 border-cyan-500 dark:border-cyan-400 shadow-md'
                    : 'hover:bg-gradient-to-r hover:from-cyan-50 hover:to-cyan-100 dark:hover:from-cyan-900/20 dark:hover:to-cyan-800/20'
                }`}
                whileHover={{
                  scale: 1.02,
                  transition: { duration: 0.2 }
                }}
                whileTap={{
                  scale: 0.98,
                  transition: { duration: 0.1 }
                }}
              >
                {/* Efeito de brilho no hover */}
                <div className={`absolute inset-0 transition-opacity duration-300 ${
                  hasUnread
                    ? 'bg-gradient-to-r from-cyan-500/10 via-blue-500/5 to-cyan-700/10 opacity-50 group-hover:opacity-70'
                    : 'bg-gradient-to-r from-cyan-500/5 via-transparent to-cyan-700/5 opacity-0 group-hover:opacity-100'
                }`}></div>

                {/* Avatar */}
                <div className="relative flex-shrink-0 z-10">
                  {image ? (
                    <div className="relative">
                      <img
                        src={image}
                        alt={name}
                        className={`h-12 w-12 rounded-2xl object-cover transition-all duration-300 ${
                          hasUnread
                            ? 'ring-3 ring-cyan-400 dark:ring-cyan-300 group-hover:ring-cyan-500 dark:group-hover:ring-cyan-200 shadow-lg'
                            : 'ring-2 ring-cyan-200 dark:ring-cyan-800 group-hover:ring-cyan-300 dark:group-hover:ring-cyan-700'
                        }`}
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.style.display = 'none';
                        }}
                      />
                      {conversation.isOnline && (
                        <div className="absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-green-500 border-2 border-white dark:border-gray-900 shadow-sm"></div>
                      )}
                    </div>
                  ) : conversation.type === 'GROUP' ? (
                    <div className="relative">
                      <div className={`h-12 w-12 rounded-2xl bg-gradient-to-br from-cyan-500 to-cyan-600 flex items-center justify-center text-white shadow-lg transition-all duration-300 ${
                        hasUnread
                          ? 'ring-3 ring-cyan-400 dark:ring-cyan-300 group-hover:ring-cyan-500 dark:group-hover:ring-cyan-200'
                          : 'ring-2 ring-cyan-200 dark:ring-cyan-800 group-hover:ring-cyan-300 dark:group-hover:ring-cyan-700'
                      }`}>
                        <Users size={20} />
                      </div>
                      {conversation.isOnline && (
                        <div className="absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-green-500 border-2 border-white dark:border-gray-900 shadow-sm"></div>
                      )}
                    </div>
                  ) : (
                    <div className="relative">
                      <div className={`h-12 w-12 rounded-2xl bg-gradient-to-br from-cyan-500 to-cyan-600 flex items-center justify-center text-white font-semibold shadow-lg transition-all duration-300 ${
                        hasUnread
                          ? 'ring-3 ring-cyan-400 dark:ring-cyan-300 group-hover:ring-cyan-500 dark:group-hover:ring-cyan-200'
                          : 'ring-2 ring-cyan-200 dark:ring-cyan-800 group-hover:ring-cyan-300 dark:group-hover:ring-cyan-700'
                      }`}>
                        {getInitials(name)}
                      </div>
                      {conversation.isOnline && (
                        <div className="absolute -bottom-1 -right-1 h-4 w-4 rounded-full bg-green-500 border-2 border-white dark:border-gray-900 shadow-sm"></div>
                      )}
                    </div>
                  )}
                </div>

                {/* Informações */}
                <div className="flex-1 min-w-0 z-10">
                  <div className="flex justify-between items-start mb-1">
                    <h4 className={`truncate transition-colors duration-300 ${
                      hasUnread
                        ? 'font-bold text-gray-900 dark:text-white group-hover:text-cyan-700 dark:group-hover:text-cyan-300'
                        : 'font-semibold text-gray-900 dark:text-gray-100 group-hover:text-cyan-600 dark:group-hover:text-cyan-400'
                    }`}>
                      {name}
                    </h4>
                    {lastMessage && (
                      <span className="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap ml-2">
                        {formatLastMessageTime(lastMessage.createdAt)}
                      </span>
                    )}
                  </div>

                  {lastMessage ? (
                    <p className={`text-sm truncate transition-colors duration-300 ${
                      hasUnread 
                        ? 'font-medium text-gray-900 dark:text-gray-100' 
                        : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {lastMessage.content}
                    </p>
                  ) : (
                    <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                      Nenhuma mensagem
                    </p>
                  )}
                </div>

                {/* Indicador de não lidas */}
                <AnimatePresence>
                  {hasUnread && (
                    <motion.div
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0, opacity: 0 }}
                      transition={{ type: "spring", stiffness: 500, damping: 30 }}
                      className="flex-shrink-0 z-10 flex flex-col items-center gap-1"
                    >
                      {/* Ponto principal */}
                      <div className="h-4 w-4 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 shadow-lg flex items-center justify-center">
                        <div className="h-2 w-2 rounded-full bg-white animate-pulse"></div>
                      </div>
                      {/* Texto "Nova" */}
                      <span className="text-xs font-bold text-cyan-600 dark:text-cyan-400 uppercase tracking-wide">
                        Nova
                      </span>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.button>
            );
          })}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

export default ChatList;
