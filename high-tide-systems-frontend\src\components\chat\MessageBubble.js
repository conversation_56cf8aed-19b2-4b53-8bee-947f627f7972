'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Check, CheckCheck, Clock, AlertCircle } from 'lucide-react';
import AttachmentViewer from './AttachmentViewer';

const MessageBubble = ({ 
  message, 
  isCurrentUser, 
  isSelected = false, 
  isGroupChat = false, 
  senderName = '', 
  senderInitials = 'U', 
  onToggleSelection,
  onLongPress 
}) => {
  // Formatar hora da mensagem
  const formatMessageTime = (dateString) => {
    return format(new Date(dateString), 'HH:mm', { locale: ptBR });
  };

  // Determinar se a mensagem tem anexos
  const hasAttachments = message.contentType === 'ATTACHMENT' && 
                        message.metadata?.attachments && 
                        message.metadata.attachments.length > 0;
  

  // Obter status da mensagem
  const getMessageStatus = () => {
    if (message.failed) return 'failed';
    if (message.isTemp) return 'sending';
    // Aqui você pode implementar lógica mais complexa baseada nos status da mensagem
    return 'sent';
  };

  const messageStatus = getMessageStatus();

  // Ícone do status da mensagem
  const getStatusIcon = () => {
    switch (messageStatus) {
      case 'sending':
        return <Clock size={14} className="text-gray-400" />;
      case 'sent':
        return <Check size={14} className="text-gray-400" />;
      case 'delivered':
        return <CheckCheck size={14} className="text-gray-400" />;
      case 'READ':
        return <CheckCheck size={14} className="text-cyan-500" />;
      case 'failed':
        return <AlertCircle size={14} className="text-red-500" />;
      default:
        return null;
    }
  };

  const bubbleVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: { 
      scale: 1, 
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    selected: {
      scale: 1.02,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 30
      }
    }
  };

  return (
    <motion.div
      key={message.id}
      variants={bubbleVariants}
      initial="hidden"
      animate={isSelected ? "selected" : "visible"}
      className={`message-bubble flex ${isCurrentUser ? 'justify-end' : 'justify-start'} mb-3`}
      onClick={() => onToggleSelection && onToggleSelection(message)}
      onContextMenu={(e) => {
        e.preventDefault();
        onLongPress && onLongPress(message);
      }}
    >
      <div className={`flex max-w-[70%] ${isCurrentUser ? 'flex-row-reverse' : 'flex-row'} items-end gap-2`}>
        {/* Avatar do remetente (apenas para mensagens de outros usuários em grupos) */}
        {!isCurrentUser && isGroupChat && (
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-cyan-400 to-cyan-600 flex items-center justify-center text-white text-xs font-medium mb-1 flex-shrink-0">
            {senderInitials}
          </div>
        )}

        <div className={`message-content ${isCurrentUser ? 'items-end' : 'items-start'} flex flex-col`}>
          {/* Nome do remetente (apenas para mensagens de outros usuários em grupos) */}
          {!isCurrentUser && isGroupChat && (
            <span className="text-xs text-gray-500 dark:text-gray-400 mb-1 px-2">
              {senderName}
            </span>
          )}

          {/* Balão da mensagem */}
          <motion.div
            className={`relative rounded-2xl px-4 py-3 shadow-sm ${
              isSelected 
                ? 'ring-2 ring-cyan-500/50 shadow-lg shadow-cyan-500/20' 
                : ''
            } ${
              isCurrentUser
                ? 'bg-gradient-to-br from-cyan-500 via-cyan-600 to-cyan-700 text-white'
                : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-700'
            }`}
            style={{
              borderTopLeftRadius: !isCurrentUser && isGroupChat ? '8px' : '16px',
              borderTopRightRadius: isCurrentUser ? '8px' : '16px',
              ...(isCurrentUser && {
                boxShadow: "0 4px 14px 0 rgba(6, 182, 212, 0.25)"
              })
            }}
          >
            {/* Conteúdo da mensagem */}
            <div className="message-text">
              {/* Anexos */}
              {hasAttachments && (
                <div className="attachments mb-2 space-y-2">
                  {message.metadata.attachments.map((attachment, index) => (
                    <AttachmentViewer
                      key={`${message.id}-attachment-${index}`}
                      attachment={attachment}
                      compact={true}
                    />
                  ))}
                </div>
              )}

              {/* Texto da mensagem */}
              {message.content && message.content.trim() && (
                <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
                  {message.content}
                </p>
              )}


            </div>

            {/* Informações da mensagem (hora e status) */}
            <div className={`flex items-center gap-1 mt-2 ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>
              <span className={`text-xs ${
                isCurrentUser 
                  ? 'text-cyan-100/80' 
                  : 'text-gray-500 dark:text-gray-400'
              }`}>
                {formatMessageTime(message.createdAt)}
              </span>
              
              {/* Status apenas para mensagens do usuário atual */}
              {isCurrentUser && (
                <div className="ml-1">
                  {getStatusIcon()}
                </div>
              )}
            </div>

            {/* Indicador de falha na mensagem */}
            {message.failed && (
              <div className="absolute -bottom-6 right-0 text-xs text-red-500 dark:text-red-400 flex items-center gap-1">
                <AlertCircle size={12} />
                <span>Falha no envio</span>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default MessageBubble;