"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/contexts/ChatContext.js":
/*!*************************************!*\
  !*** ./src/contexts/ChatContext.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   useChat: () => (/* binding */ useChat),\n/* harmony export */   useChatActions: () => (/* binding */ useChatActions),\n/* harmony export */   useChatData: () => (/* binding */ useChatData),\n/* harmony export */   useChatSocket: () => (/* binding */ useChatSocket),\n/* harmony export */   useChatUI: () => (/* binding */ useChatUI)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_chat_useSocketConnection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hooks/chat/useSocketConnection */ \"(app-pages-browser)/./src/hooks/chat/useSocketConnection.js\");\n/* harmony import */ var _hooks_chat_useCache__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/chat/useCache */ \"(app-pages-browser)/./src/hooks/chat/useCache.js\");\n/* harmony import */ var _hooks_chat_useConversations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/chat/useConversations */ \"(app-pages-browser)/./src/hooks/chat/useConversations.js\");\n/* harmony import */ var _hooks_chat_useMessages__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/chat/useMessages */ \"(app-pages-browser)/./src/hooks/chat/useMessages.js\");\n/* harmony import */ var _hooks_chat_useChatUI__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../hooks/chat/useChatUI */ \"(app-pages-browser)/./src/hooks/chat/useChatUI.js\");\n/* harmony import */ var _hooks_chat_useSocketEvents__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../hooks/chat/useSocketEvents */ \"(app-pages-browser)/./src/hooks/chat/useSocketEvents.js\");\n/* __next_internal_client_entry_do_not_use__ ChatProvider,useChat,useChatData,useChatActions,useChatUI,useChatSocket auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$();\n\n\n// Import custom hooks\n\n\n\n\n\n\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst ChatProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    // Initialize hooks\n    const { socket, isConnected, handleAuthError } = (0,_hooks_chat_useSocketConnection__WEBPACK_IMPORTED_MODULE_3__.useSocketConnection)();\n    const { clearCacheForUser } = (0,_hooks_chat_useCache__WEBPACK_IMPORTED_MODULE_4__.useCache)();\n    const { conversations, activeConversation, setActiveConversation, loadConversations, createConversation, createOrGetConversation, addParticipantToGroup, addMultipleParticipantsToGroup, removeParticipantFromGroup, cleanupLeftConversations, updateConversationLastMessage } = (0,_hooks_chat_useConversations__WEBPACK_IMPORTED_MODULE_5__.useConversations)(socket, handleAuthError);\n    const { messages, unreadCount, loadMessages, sendMessage, loadUnreadCount, markMessagesAsRead, resetUnreadCount, deleteMessages, addMessage, setUnreadCount } = (0,_hooks_chat_useMessages__WEBPACK_IMPORTED_MODULE_6__.useMessages)(socket, handleAuthError);\n    const { isPanelOpen, isModalOpen, isLoading, toggleChatPanel, toggleChatModal, setIsPanelOpen, setIsModalOpen, setIsLoading } = (0,_hooks_chat_useChatUI__WEBPACK_IMPORTED_MODULE_7__.useChatUI)();\n    // Setup socket events\n    const { joinConversationRoom, leaveConversationRoom, emitTyping } = (0,_hooks_chat_useSocketEvents__WEBPACK_IMPORTED_MODULE_8__.useSocketEvents)(socket, isConnected, {\n        addMessage,\n        setUnreadCount,\n        loadUnreadCount,\n        loadConversations,\n        updateConversationLastMessage\n    });\n    // Limpar estado do chat quando o usuário muda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (user) {\n                console.log('Usuário logado, limpando estado do chat para novo usuário:', user.fullName);\n                clearCacheForUser();\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id,\n        clearCacheForUser\n    ]);\n    // Carregar dados iniciais quando conectado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (user && socket && isConnected) {\n                console.log('Socket conectado, carregando dados iniciais do chat...');\n                loadConversations();\n                loadUnreadCount();\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        socket,\n        isConnected,\n        loadConversations,\n        loadUnreadCount\n    ]);\n    // Context value\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatProvider.useMemo[contextValue]\": ()=>({\n                // Connection state\n                socket,\n                isConnected,\n                // Data\n                conversations,\n                messages,\n                unreadCount,\n                activeConversation,\n                // UI state\n                isPanelOpen,\n                isModalOpen,\n                isLoading,\n                // Conversation actions\n                loadConversations,\n                createConversation,\n                createOrGetConversation,\n                setActiveConversation,\n                addParticipantToGroup,\n                addMultipleParticipantsToGroup,\n                removeParticipantFromGroup,\n                cleanupLeftConversations,\n                // Message actions\n                loadMessages,\n                sendMessage,\n                loadUnreadCount,\n                markMessagesAsRead,\n                resetUnreadCount,\n                deleteMessages,\n                // UI actions\n                toggleChatPanel,\n                toggleChatModal,\n                setIsPanelOpen,\n                setIsModalOpen,\n                setIsLoading,\n                // Socket events\n                joinConversationRoom,\n                leaveConversationRoom,\n                emitTyping\n            })\n    }[\"ChatProvider.useMemo[contextValue]\"], [\n        socket,\n        isConnected,\n        conversations,\n        messages,\n        unreadCount,\n        activeConversation,\n        isPanelOpen,\n        isModalOpen,\n        isLoading,\n        loadConversations,\n        createConversation,\n        createOrGetConversation,\n        setActiveConversation,\n        addParticipantToGroup,\n        addMultipleParticipantsToGroup,\n        removeParticipantFromGroup,\n        cleanupLeftConversations,\n        loadMessages,\n        sendMessage,\n        loadUnreadCount,\n        markMessagesAsRead,\n        resetUnreadCount,\n        deleteMessages,\n        toggleChatPanel,\n        toggleChatModal,\n        setIsPanelOpen,\n        setIsModalOpen,\n        setIsLoading,\n        joinConversationRoom,\n        leaveConversationRoom,\n        emitTyping\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\contexts\\\\ChatContext.js\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatProvider, \"M54hfCvBYmcs7grujOEOJtEXZ7c=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_chat_useSocketConnection__WEBPACK_IMPORTED_MODULE_3__.useSocketConnection,\n        _hooks_chat_useCache__WEBPACK_IMPORTED_MODULE_4__.useCache,\n        _hooks_chat_useConversations__WEBPACK_IMPORTED_MODULE_5__.useConversations,\n        _hooks_chat_useMessages__WEBPACK_IMPORTED_MODULE_6__.useMessages,\n        _hooks_chat_useChatUI__WEBPACK_IMPORTED_MODULE_7__.useChatUI,\n        _hooks_chat_useSocketEvents__WEBPACK_IMPORTED_MODULE_8__.useSocketEvents\n    ];\n});\n_c = ChatProvider;\n// Hook para usar o contexto\nconst useChat = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (!context) {\n        throw new Error('useChat must be used within a ChatProvider');\n    }\n    return context;\n};\n_s1(useChat, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Hook específico para dados do chat (conversas e mensagens)\nconst useChatData = ()=>{\n    _s2();\n    const { conversations, messages, unreadCount, activeConversation } = useChat();\n    return {\n        conversations,\n        messages,\n        unreadCount,\n        activeConversation\n    };\n};\n_s2(useChatData, \"fYQD90NOAxWhiN8ZxKN9gngYOJA=\", false, function() {\n    return [\n        useChat\n    ];\n});\n// Hook específico para ações do chat\nconst useChatActions = ()=>{\n    _s3();\n    const { loadConversations, createConversation, createOrGetConversation, setActiveConversation, addParticipantToGroup, addMultipleParticipantsToGroup, removeParticipantFromGroup, loadMessages, sendMessage, loadUnreadCount, markMessagesAsRead, resetUnreadCount, deleteMessages } = useChat();\n    return {\n        loadConversations,\n        createConversation,\n        createOrGetConversation,\n        setActiveConversation,\n        addParticipantToGroup,\n        addMultipleParticipantsToGroup,\n        removeParticipantFromGroup,\n        loadMessages,\n        sendMessage,\n        loadUnreadCount,\n        markMessagesAsRead,\n        resetUnreadCount,\n        deleteMessages\n    };\n};\n_s3(useChatActions, \"HLXnAyrGp6sNp9v45E3v3w7cSns=\", false, function() {\n    return [\n        useChat\n    ];\n});\n// Hook específico para UI do chat\nconst useChatUI = ()=>{\n    _s4();\n    const { isPanelOpen, isModalOpen, isLoading, toggleChatPanel, toggleChatModal, setIsPanelOpen, setIsModalOpen, setIsLoading } = useChat();\n    return {\n        isPanelOpen,\n        isModalOpen,\n        isLoading,\n        toggleChatPanel,\n        toggleChatModal,\n        setIsPanelOpen,\n        setIsModalOpen,\n        setIsLoading\n    };\n};\n_s4(useChatUI, \"a1GdI6HcQjprunap1HGjmNTLU8c=\", false, function() {\n    return [\n        useChat\n    ];\n});\n// Hook específico para conexão socket\nconst useChatSocket = ()=>{\n    _s5();\n    const { socket, isConnected, joinConversationRoom, leaveConversationRoom, emitTyping } = useChat();\n    return {\n        socket,\n        isConnected,\n        joinConversationRoom,\n        leaveConversationRoom,\n        emitTyping\n    };\n};\n_s5(useChatSocket, \"97j3WZm3iS/mGSui2WVLGombvmo=\", false, function() {\n    return [\n        useChat\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"ChatProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ChatContext.js\n"));

/***/ })

});