import { useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

export const useChatAPI = () => {
  const { user } = useAuth();

  // Obter o token atual do localStorage
  const getCurrentToken = useCallback(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('token');
    }
    return null;
  }, []);

  // Função auxiliar para fazer requisições HTTP
  const makeRequest = useCallback(async (endpoint, options = {}) => {
    const token = getCurrentToken();
    if (!token) {
      throw new Error('Token não encontrado');
    }

    const url = `${API_URL}${endpoint}`;
    const defaultOptions = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    const finalOptions = { ...defaultOptions, ...options };

    const response = await fetch(url, finalOptions);
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('Unauthorized');
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }, [getCurrentToken]);

  // API functions
  const fetchConversations = useCallback(async () => {
    try {
      const response = await makeRequest('/chat/conversations');
      return response.success ? response.data : [];
    } catch (error) {
      console.error('Erro ao carregar conversas:', error);
      throw error;
    }
  }, [makeRequest]);

  const fetchMessages = useCallback(async (conversationId, page = 1, limit = 50) => {
    try {
      const response = await makeRequest(`/chat/conversations/${conversationId}/messages?page=${page}&limit=${limit}`);
      return response.success ? response.data : [];
    } catch (error) {
      console.error('Erro ao carregar mensagens:', error);
      throw error;
    }
  }, [makeRequest]);

  const fetchUnreadCount = useCallback(async () => {
    try {
      const response = await makeRequest('/chat/messages/unread');
      return response.success ? response.data : 0;
    } catch (error) {
      console.error('Erro ao carregar contagem de não lidas:', error);
      return 0;
    }
  }, [makeRequest]);

  const sendMessageAPI = useCallback(async (conversationId, content, contentType = 'TEXT', metadata = null) => {
    try {
      const response = await makeRequest(`/chat/conversations/${conversationId}/messages`, {
        method: 'POST',
        body: JSON.stringify({
          content,
          contentType,
          metadata
        })
      });
      return response.success ? response.data : null;
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      throw error;
    }
  }, [makeRequest]);

  const createConversationAPI = useCallback(async (participantIds, title = null) => {
    try {
      const response = await makeRequest('/chat/conversations', {
        method: 'POST',
        body: JSON.stringify({
          participants: participantIds.map(id => ({ userId: id })),
          title
        })
      });
      return response.success ? response.data : null;
    } catch (error) {
      console.error('Erro ao criar conversa:', error);
      throw error;
    }
  }, [makeRequest]);

  const createOrGetConversationAPI = useCallback(async (otherUserId) => {
    try {
      const response = await makeRequest('/chat/conversations/individual', {
        method: 'POST',
        body: JSON.stringify({
          otherUserId
        })
      });
      return response.success ? response.data : null;
    } catch (error) {
      console.error('Erro ao criar/obter conversa individual:', error);
      throw error;
    }
  }, [makeRequest]);

  const markMessagesAsReadAPI = useCallback(async (conversationId, messageId) => {
    try {
      const response = await makeRequest('/chat/messages/mark-read', {
        method: 'POST',
        body: JSON.stringify({
          conversationId,
          messageId
        })
      });
      return response.success;
    } catch (error) {
      console.error('Erro ao marcar mensagens como lidas:', error);
      return false;
    }
  }, [makeRequest]);

  const deleteMessagesAPI = useCallback(async (messageIds, conversationId) => {
    try {
      const response = await makeRequest(`/chat/conversations/${conversationId}/messages/delete`, {
        method: 'DELETE',
        body: JSON.stringify({ messageIds })
      });
      return response.success;
    } catch (error) {
      console.error('Erro ao deletar mensagens:', error);
      return false;
    }
  }, [makeRequest]);

  const addParticipantAPI = useCallback(async (conversationId, participantId) => {
    try {
      const response = await makeRequest(`/chat/conversations/${conversationId}/participants`, {
        method: 'POST',
        body: JSON.stringify({
          participants: [{ userId: participantId }]
        })
      });
      return response.success;
    } catch (error) {
      console.error('Erro ao adicionar participante:', error);
      return false;
    }
  }, [makeRequest]);

  const addMultipleParticipantsAPI = useCallback(async (conversationId, participants) => {
    try {
      const response = await makeRequest(`/chat/conversations/${conversationId}/participants`, {
        method: 'POST',
        body: JSON.stringify({ participants })
      });
      return response.success;
    } catch (error) {
      console.error('Erro ao adicionar múltiplos participantes:', error);
      return false;
    }
  }, [makeRequest]);

  const removeParticipantAPI = useCallback(async (conversationId, participantId) => {
    try {
      const response = await makeRequest(`/chat/conversations/${conversationId}/participants/${participantId}`, {
        method: 'DELETE'
      });
      return response.success;
    } catch (error) {
      console.error('Erro ao remover participante:', error);
      return false;
    }
  }, [makeRequest]);

  return {
    // Core API functions
    fetchConversations,
    fetchMessages,
    fetchUnreadCount,
    sendMessageAPI,
    createConversationAPI,
    createOrGetConversationAPI,
    markMessagesAsReadAPI,
    deleteMessagesAPI,
    
    // Participant management
    addParticipantAPI,
    addMultipleParticipantsAPI,
    removeParticipantAPI,
    
    // Utility
    makeRequest
  };
};