// src/controllers/chat/messageController.js
const messageService = require('../../services/chat/messageService');
const errorHandler = require('../../utils/errorHandler');

/**
 * Busca mensagens de uma conversa
 * @param {Object} req - Requisição Express
 * @param {Object} res - Resposta Express
 */
const getConversationMessages = async (req, res) => {
  try {
    const userId = req.user.id;
    const conversationId = req.params.conversationId;
    const { limit, before, after } = req.query;
    
    const messages = await messageService.getConversationMessages(
      conversationId,
      userId,
      {
        limit: limit ? parseInt(limit) : undefined,
        before,
        after
      }
    );
    
    res.status(200).json({
      success: true,
      data: messages
    });
  } catch (error) {
    const errorResponse = errorHandler.handleError(error);
    res.status(errorResponse.status).json({
      success: false,
      error: errorResponse.message,
      errors: errorResponse.errors
    });
  }
};

/**
 * Cria uma nova mensagem
 * @param {Object} req - Requisição Express
 * @param {Object} res - Resposta Express
 */
const createMessage = async (req, res) => {
  try {
    const userId = req.user.id;
    const conversationId = req.params.conversationId;
    
    const message = await messageService.createMessage({
      ...req.body,
      conversationId,
      senderId: userId
    });
    
    res.status(201).json({
      success: true,
      data: message
    });
  } catch (error) {
    const errorResponse = errorHandler.handleError(error);
    res.status(errorResponse.status).json({
      success: false,
      error: errorResponse.message,
      errors: errorResponse.errors
    });
  }
};

/**
 * Exclui uma mensagem
 * @param {Object} req - Requisição Express
 * @param {Object} res - Resposta Express
 */
const deleteMessage = async (req, res) => {
  try {
    const userId = req.user.id;
    const messageId = req.params.messageId;
    
    const message = await messageService.deleteMessage(messageId, userId);
    
    res.status(200).json({
      success: true,
      data: message
    });
  } catch (error) {
    const errorResponse = errorHandler.handleError(error);
    res.status(errorResponse.status).json({
      success: false,
      error: errorResponse.message,
      errors: errorResponse.errors
    });
  }
};

/**
 * Busca mensagens não lidas do usuário
 * @param {Object} req - Requisição Express
 * @param {Object} res - Resposta Express
 */
const getUnreadMessages = async (req, res) => {
  try {
    const userId = req.user.id;
    
    const unreadMessages = await messageService.getUnreadMessages(userId);
    
    res.status(200).json({
      success: true,
      data: unreadMessages
    });
  } catch (error) {
    const errorResponse = errorHandler.handleError(error);
    res.status(errorResponse.status).json({
      success: false,
      error: errorResponse.message,
      errors: errorResponse.errors
    });
  }
};

/**
 * Marca mensagens como lidas
 * @param {Object} req - Requisição Express
 * @param {Object} res - Resposta Express
 */
const markMessagesAsRead = async (req, res) => {
  try {
    const userId = req.user.id;
    const { conversationId, messageId } = req.body;

    const result = await messageService.markMessagesAsRead(userId, conversationId, messageId);
    
    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    const errorResponse = errorHandler.handleError(error);
    res.status(errorResponse.status).json({
      success: false,
      error: errorResponse.message,
      errors: errorResponse.errors
    });
  }
};

/**
 * Marca mensagem específica como lida via URL params
 * @param {Object} req - Requisição Express
 * @param {Object} res - Resposta Express
 */
const markMessageAsReadByUrl = async (req, res) => {
  try {
    const userId = req.user.id;
    const { conversationId, messageId } = req.params;
    
    const result = await messageService.markMessagesAsRead(userId, conversationId, messageId);
    
    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    const errorResponse = errorHandler.handleError(error);
    res.status(errorResponse.status).json({
      success: false,
      error: errorResponse.message,
      errors: errorResponse.errors
    });
  }
};

/**
 * Reseta contador de mensagens não lidas
 * @param {Object} req - Requisição Express
 * @param {Object} res - Resposta Express
 */
const resetUnreadCount = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Marcar todas as conversas como lidas
    const result = await messageService.resetAllUnreadMessages(userId);
    
    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    const errorResponse = errorHandler.handleError(error);
    res.status(errorResponse.status).json({
      success: false,
      error: errorResponse.message,
      errors: errorResponse.errors
    });
  }
};

module.exports = {
  getConversationMessages,
  createMessage,
  deleteMessage,
  getUnreadMessages,
  markMessagesAsRead,
  markMessageAsReadByUrl,
  resetUnreadCount
};
