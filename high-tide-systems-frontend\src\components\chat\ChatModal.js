'use client';

import React, { useState, useEffect } from 'react';
import { useChat } from '@/contexts/ChatContext';
import { useAuth } from '@/contexts/AuthContext';
import { X, Minimize2, MessageCircle, Search, Plus, Users } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { ModuleModal } from '@/components/ui';
import ChatList from './ChatList';
import ChatConversation from './ChatConversation';
import UserSearch from './UserSearch';
import GroupCreation from './GroupCreation';

const ChatModal = () => {
  const {
    isModalOpen,
    toggleChatModal,
    activeConversation,
    setActiveConversation,
    createOrGetConversation,
    createConversation,
    isLoading
  } = useChat();
  const { user } = useAuth();

  const [searchQuery, setSearchQuery] = useState('');
  const [showUserSearch, setShowUserSearch] = useState(false);
  const [showGroupCreation, setShowGroupCreation] = useState(false);

  // Se o usuário não estiver logado e o modal estiver aberto, fechar o modal
  useEffect(() => {
    if (!user && isModalOpen) {
      toggleChatModal();
    }
  }, [user, isModalOpen, toggleChatModal]);

  // Variantes de animação
  const modalVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { type: 'spring', stiffness: 300, damping: 24 }
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      transition: { duration: 0.2 }
    }
  };

  return (
    <AnimatePresence>
      {isModalOpen && user && (
        <ModuleModal
          isOpen={isModalOpen}
          onClose={toggleChatModal}
          title="Chat"
          icon={<MessageCircle size={22} />}
          moduleColor="chat"
          size="xl"
          className="w-[90vw] max-w-[1400px]"
        >
          <div className="flex h-[85vh] w-full">
            {/* Sidebar com lista de conversas */}
            <div className="w-2/4 border-r border-cyan-500 dark:border-cyan-400 bg-background flex flex-col">
              {/* Barra de pesquisa */}
              <div className="p-4 border-b border-cyan-500 dark:border-cyan-400 bg-background">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Pesquisar conversas..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full py-2.5 pl-10 pr-4 bg-background rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-500 dark:focus:ring-cyan-600 text-foreground border border-cyan-500 dark:border-cyan-400"
                  />
                  <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-cyan-500 dark:text-cyan-400" />
                </div>
              </div>

              {/* Lista de conversas */}
              <div className="flex-1 overflow-y-auto relative">
                {isLoading ? (
                  <div className="absolute inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80 z-10">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-cyan-500"></div>
                  </div>
                ) : null}
                <ChatList searchQuery={searchQuery} />
              </div>

              {/* Botões de nova conversa */}
              <div className="p-4 border-t border-cyan-500 dark:border-cyan-400 bg-background">
                {user ? (
                  <div className={`grid gap-2 ${user.isClient || user.role === 'CLIENT' ? 'grid-cols-1' : 'grid-cols-2'}`}>
                    <button
                      onClick={() => setShowUserSearch(true)}
                      className="flex items-center justify-center gap-2 py-2.5 bg-gradient-to-r from-cyan-500 to-cyan-700 hover:from-cyan-600 hover:to-cyan-800 dark:from-cyan-600 dark:to-cyan-800 dark:hover:from-cyan-700 dark:hover:to-cyan-900 text-white rounded-lg transition-colors shadow-sm"
                    >
                      <Plus size={18} />
                      <span className="font-medium">Nova Conversa</span>
                    </button>
                    {/* Clientes não podem criar grupos, apenas ser adicionados */}
                    {!user.isClient && user.role !== 'CLIENT' && (
                      <button
                        onClick={() => setShowGroupCreation(true)}
                        className="flex items-center justify-center gap-2 py-2.5 bg-gradient-to-r from-cyan-600 to-cyan-800 hover:from-cyan-700 hover:to-cyan-900 dark:from-cyan-700 dark:to-cyan-900 dark:hover:from-cyan-800 dark:hover:to-cyan-950 text-white rounded-lg transition-colors shadow-sm"
                      >
                        <Users size={18} />
                        <span className="font-medium">Novo Grupo</span>
                      </button>
                    )}
                  </div>
                ) : (
                  <button
                    onClick={() => window.location.href = '/login'}
                    className="w-full flex items-center justify-center gap-2 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
                  >
                    <span>Faça login para conversar</span>
                  </button>
                )}
              </div>
            </div>

            {/* Área de conversa */}
            <div className="w-3/4 flex flex-col bg-background">
              {showUserSearch ? (
                <UserSearch
                  onSelectUser={(selectedUser) => {
                    console.log('ChatModal: Selecionou usuário para criar conversa:', selectedUser);
                    createOrGetConversation(selectedUser)
                      .then((conversation) => {
                        console.log('ChatModal: Conversa criada/obtida com sucesso:', conversation);
                        if (conversation?.id) {
                          // Definir a conversa como ativa
                          setActiveConversation(conversation.id);
                          console.log('ChatModal: Conversa ativada:', conversation.id);
                        }
                        // Forçar atualização da lista de conversas
                        setTimeout(() => {
                          console.log('ChatModal: Forçando atualização da lista de conversas');
                          window.dispatchEvent(new CustomEvent('chat:websocket:update', {
                            detail: { type: 'conversations', timestamp: Date.now() }
                          }));
                        }, 500);
                        setShowUserSearch(false);
                      })
                      .catch(error => {
                        console.error('Erro ao criar/obter conversa:', error);
                        setShowUserSearch(false);
                      });
                  }}
                  onClose={() => setShowUserSearch(false)}
                />
              ) : showGroupCreation ? (
                <GroupCreation
                  onCreateGroup={(participantIds, title) => {
                    console.log('ChatModal: Chamando createConversation');
                    console.log('createConversation existe?', typeof createConversation === 'function');

                    if (typeof createConversation !== 'function') {
                      console.error('createConversation não é uma função');
                      return;
                    }

                    createConversation(participantIds, title)
                      .then((result) => {
                        console.log('Grupo criado com sucesso:', result);
                        // Forçar atualização da lista de conversas
                        setTimeout(() => {
                          console.log('ChatModal: Forçando atualização da lista de conversas após criar grupo');
                          window.dispatchEvent(new CustomEvent('chat:websocket:update', {
                            detail: { type: 'conversations', timestamp: Date.now() }
                          }));
                        }, 500);
                        setShowGroupCreation(false);
                      })
                      .catch(error => {
                        console.error('Erro ao criar grupo:', error);
                        setShowGroupCreation(false);
                      });
                  }}
                  onBack={() => setShowGroupCreation(false)}
                />
              ) : activeConversation ? (
                <ChatConversation
                  conversationId={activeConversation}
                  onBack={() => setActiveConversation(null)}
                />
              ) : (
                <div className="flex flex-col items-center justify-center h-full p-6 text-gray-500 dark:text-gray-400">
                  <div className="bg-cyan-100 dark:bg-cyan-900/30 p-6 rounded-full mb-6 shadow-md border border-cyan-500 dark:border-cyan-400">
                    <MessageCircle size={64} className="text-cyan-500 dark:text-cyan-400" />
                  </div>
                  <h3 className="text-2xl font-medium text-gray-900 dark:text-gray-100 mb-3">
                    Bem-vindo ao Chat!
                  </h3>
                  <p className="text-center mb-6 max-w-md">
                    Selecione uma conversa existente ou crie uma nova para começar a conversar.
                  </p>
                  <div className="flex gap-3 justify-center">
                    <button
                      onClick={() => setShowUserSearch(true)}
                      className="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-cyan-500 to-cyan-700 hover:from-cyan-600 hover:to-cyan-800 dark:from-cyan-600 dark:to-cyan-800 dark:hover:from-cyan-700 dark:hover:to-cyan-900 text-white rounded-lg transition-colors shadow-sm"
                    >
                      <Plus size={18} />
                      <span>Nova Conversa</span>
                    </button>
                    {/* Clientes não podem criar grupos, apenas ser adicionados */}
                    {!user.isClient && user.role !== 'CLIENT' && (
                      <button
                        onClick={() => setShowGroupCreation(true)}
                        className="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-cyan-600 to-cyan-800 hover:from-cyan-700 hover:to-cyan-900 dark:from-cyan-700 dark:to-cyan-900 dark:hover:from-cyan-800 dark:hover:to-cyan-950 text-white rounded-lg transition-colors shadow-sm"
                      >
                        <Users size={18} />
                        <span>Novo Grupo</span>
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </ModuleModal>
      )}
    </AnimatePresence>
  );
};

export default ChatModal;
