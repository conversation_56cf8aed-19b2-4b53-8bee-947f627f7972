"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/hooks/chat/useChatAPI.js":
/*!**************************************!*\
  !*** ./src/hooks/chat/useChatAPI.js ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useChatAPI: () => (/* binding */ useChatAPI)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\nvar _s = $RefreshSig$();\n\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';\nconst useChatAPI = ()=>{\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    // Obter o token atual do localStorage\n    const getCurrentToken = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatAPI.useCallback[getCurrentToken]\": ()=>{\n            if (true) {\n                return localStorage.getItem('token');\n            }\n            return null;\n        }\n    }[\"useChatAPI.useCallback[getCurrentToken]\"], []);\n    // Função auxiliar para fazer requisições HTTP\n    const makeRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatAPI.useCallback[makeRequest]\": async function(endpoint) {\n            let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n            const token = getCurrentToken();\n            if (!token) {\n                throw new Error('Token não encontrado');\n            }\n            const url = \"\".concat(API_URL).concat(endpoint);\n            const defaultOptions = {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json',\n                    ...options.headers\n                }\n            };\n            const finalOptions = {\n                ...defaultOptions,\n                ...options\n            };\n            const response = await fetch(url, finalOptions);\n            if (!response.ok) {\n                if (response.status === 401) {\n                    throw new Error('Unauthorized');\n                }\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            return response.json();\n        }\n    }[\"useChatAPI.useCallback[makeRequest]\"], [\n        getCurrentToken\n    ]);\n    // API functions\n    const fetchConversations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatAPI.useCallback[fetchConversations]\": async ()=>{\n            try {\n                const response = await makeRequest('/chat/conversations');\n                return response.success ? response.data : [];\n            } catch (error) {\n                console.error('Erro ao carregar conversas:', error);\n                throw error;\n            }\n        }\n    }[\"useChatAPI.useCallback[fetchConversations]\"], [\n        makeRequest\n    ]);\n    const fetchMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatAPI.useCallback[fetchMessages]\": async function(conversationId) {\n            let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 50;\n            try {\n                const response = await makeRequest(\"/chat/conversations/\".concat(conversationId, \"/messages?page=\").concat(page, \"&limit=\").concat(limit));\n                return response.success ? response.data : [];\n            } catch (error) {\n                console.error('Erro ao carregar mensagens:', error);\n                throw error;\n            }\n        }\n    }[\"useChatAPI.useCallback[fetchMessages]\"], [\n        makeRequest\n    ]);\n    const fetchUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatAPI.useCallback[fetchUnreadCount]\": async ()=>{\n            try {\n                const response = await makeRequest('/chat/messages/unread');\n                return response.success ? response.data : 0;\n            } catch (error) {\n                console.error('Erro ao carregar contagem de não lidas:', error);\n                return 0;\n            }\n        }\n    }[\"useChatAPI.useCallback[fetchUnreadCount]\"], [\n        makeRequest\n    ]);\n    const sendMessageAPI = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatAPI.useCallback[sendMessageAPI]\": async function(conversationId, content) {\n            let contentType = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'TEXT', metadata = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            try {\n                const response = await makeRequest(\"/chat/conversations/\".concat(conversationId, \"/messages\"), {\n                    method: 'POST',\n                    body: JSON.stringify({\n                        content,\n                        contentType,\n                        metadata\n                    })\n                });\n                return response.success ? response.data : null;\n            } catch (error) {\n                console.error('Erro ao enviar mensagem:', error);\n                throw error;\n            }\n        }\n    }[\"useChatAPI.useCallback[sendMessageAPI]\"], [\n        makeRequest\n    ]);\n    const createConversationAPI = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatAPI.useCallback[createConversationAPI]\": async function(participantIds) {\n            let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n            try {\n                const response = await makeRequest('/chat/conversations', {\n                    method: 'POST',\n                    body: JSON.stringify({\n                        participants: participantIds.map({\n                            \"useChatAPI.useCallback[createConversationAPI]\": (id)=>({\n                                    userId: id\n                                })\n                        }[\"useChatAPI.useCallback[createConversationAPI]\"]),\n                        title\n                    })\n                });\n                return response.success ? response.data : null;\n            } catch (error) {\n                console.error('Erro ao criar conversa:', error);\n                throw error;\n            }\n        }\n    }[\"useChatAPI.useCallback[createConversationAPI]\"], [\n        makeRequest\n    ]);\n    const createOrGetConversationAPI = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatAPI.useCallback[createOrGetConversationAPI]\": async (otherUserId)=>{\n            try {\n                const response = await makeRequest('/chat/conversations/individual', {\n                    method: 'POST',\n                    body: JSON.stringify({\n                        otherUserId\n                    })\n                });\n                return response.success ? response.data : null;\n            } catch (error) {\n                console.error('Erro ao criar/obter conversa individual:', error);\n                throw error;\n            }\n        }\n    }[\"useChatAPI.useCallback[createOrGetConversationAPI]\"], [\n        makeRequest\n    ]);\n    const markMessagesAsReadAPI = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatAPI.useCallback[markMessagesAsReadAPI]\": async (conversationId, messageId)=>{\n            try {\n                const response = await makeRequest('/chat/messages/mark-read', {\n                    method: 'POST',\n                    body: JSON.stringify({\n                        conversationId,\n                        messageId\n                    })\n                });\n                return response.success;\n            } catch (error) {\n                console.error('Erro ao marcar mensagens como lidas:', error);\n                return false;\n            }\n        }\n    }[\"useChatAPI.useCallback[markMessagesAsReadAPI]\"], [\n        makeRequest\n    ]);\n    const deleteMessagesAPI = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatAPI.useCallback[deleteMessagesAPI]\": async (messageIds, conversationId)=>{\n            try {\n                const response = await makeRequest(\"/chat/conversations/\".concat(conversationId, \"/messages/delete\"), {\n                    method: 'DELETE',\n                    body: JSON.stringify({\n                        messageIds\n                    })\n                });\n                return response.success;\n            } catch (error) {\n                console.error('Erro ao deletar mensagens:', error);\n                return false;\n            }\n        }\n    }[\"useChatAPI.useCallback[deleteMessagesAPI]\"], [\n        makeRequest\n    ]);\n    const addParticipantAPI = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatAPI.useCallback[addParticipantAPI]\": async (conversationId, participantId)=>{\n            try {\n                const response = await makeRequest(\"/chat/conversations/\".concat(conversationId, \"/participants\"), {\n                    method: 'POST',\n                    body: JSON.stringify({\n                        participants: [\n                            {\n                                userId: participantId\n                            }\n                        ]\n                    })\n                });\n                return response.success;\n            } catch (error) {\n                console.error('Erro ao adicionar participante:', error);\n                return false;\n            }\n        }\n    }[\"useChatAPI.useCallback[addParticipantAPI]\"], [\n        makeRequest\n    ]);\n    const addMultipleParticipantsAPI = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatAPI.useCallback[addMultipleParticipantsAPI]\": async (conversationId, participants)=>{\n            try {\n                const response = await makeRequest(\"/chat/conversations/\".concat(conversationId, \"/participants\"), {\n                    method: 'POST',\n                    body: JSON.stringify({\n                        participants\n                    })\n                });\n                return response.success;\n            } catch (error) {\n                console.error('Erro ao adicionar múltiplos participantes:', error);\n                return false;\n            }\n        }\n    }[\"useChatAPI.useCallback[addMultipleParticipantsAPI]\"], [\n        makeRequest\n    ]);\n    const removeParticipantAPI = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useChatAPI.useCallback[removeParticipantAPI]\": async (conversationId, participantId)=>{\n            try {\n                const response = await makeRequest(\"/chat/conversations/\".concat(conversationId, \"/participants/\").concat(participantId), {\n                    method: 'DELETE'\n                });\n                return response.success;\n            } catch (error) {\n                console.error('Erro ao remover participante:', error);\n                return false;\n            }\n        }\n    }[\"useChatAPI.useCallback[removeParticipantAPI]\"], [\n        makeRequest\n    ]);\n    return {\n        // Core API functions\n        fetchConversations,\n        fetchMessages,\n        fetchUnreadCount,\n        sendMessageAPI,\n        createConversationAPI,\n        createOrGetConversationAPI,\n        markMessagesAsReadAPI,\n        deleteMessagesAPI,\n        // Participant management\n        addParticipantAPI,\n        addMultipleParticipantsAPI,\n        removeParticipantAPI,\n        // Utility\n        makeRequest\n    };\n};\n_s(useChatAPI, \"sZLKHgYGqruG7rnsu9pavAC4Zsg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth\n    ];\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/chat/useChatAPI.js\n"));

/***/ })

});