"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/users/page",{

/***/ "(app-pages-browser)/./src/utils/api.js":
/*!**************************!*\
  !*** ./src/utils/api.js ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DOCUMENT_URLS: () => (/* binding */ DOCUMENT_URLS),\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// utils/api.js\n\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000',\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// Interceptor para adicionar token de autenticação e companyId em todas as requisições\napi.interceptors.request.use((config)=>{\n    const token = localStorage.getItem('token');\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    // Adicionar companyId como query parameter se disponível (para SYSTEM_ADMIN)\n    const selectedCompanyId = localStorage.getItem('systemAdminSelectedCompany');\n    if (selectedCompanyId && selectedCompanyId !== 'null' && config.url && !config.url.includes('auth/')) {\n        const separator = config.url.includes('?') ? '&' : '?';\n        config.url += \"\".concat(separator, \"companyId=\").concat(selectedCompanyId);\n    }\n    return config;\n}, (error)=>Promise.reject(error));\n// Interceptor para tratamento de erros\napi.interceptors.response.use((response)=>response, (error)=>{\n    var _error_response;\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n    }\n    return Promise.reject(error);\n});\n// Funções auxiliares para construir URLs\nconst getApiUrl = (path)=>{\n    // Remover barras iniciais extras se necessário\n    const cleanPath = path.startsWith('/') ? path.substring(1) : path;\n    return \"\".concat(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000', \"/\").concat(cleanPath);\n};\nconst DOCUMENT_URLS = {\n    get: (id)=>getApiUrl(\"documents/\".concat(id)),\n    download: (id)=>getApiUrl(\"documents/\".concat(id, \"?download=true\")),\n    upload: (targetType, targetId)=>getApiUrl(\"documents/upload?targetType=\".concat(targetType, \"&targetId=\").concat(targetId)),\n    list: (targetType, targetId)=>getApiUrl(\"documents?targetType=\".concat(targetType, \"&targetId=\").concat(targetId))\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/api.js\n"));

/***/ })

});