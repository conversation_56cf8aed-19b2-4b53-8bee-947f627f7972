import { useState, useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useChatAPI } from './useChatAPI';
import { useCache } from './useCache';

export const useConversations = (socket, onAuthError) => {
  const { user } = useAuth();
  const [conversations, setConversations] = useState([]);
  const [activeConversation, setActiveConversation] = useState(null);
  
  const { 
    fetchConversations, 
    createConversationAPI, 
    createOrGetConversationAPI,
    addParticipantAPI,
    addMultipleParticipantsAPI,
    removeParticipantAPI
  } = useChatAPI();
  
  const { 
    getCachedData, 
    setCacheData, 
    isLoadingRef,
    debouncedFunction 
  } = useCache();

  // Carregar conversas
  const loadConversations = useCallback(async (forceRefresh = true) => {
    // Verificação básica de usuário
    if (!user || !user.id) {
      console.log('Usu<PERSON>rio não encontrado, não carregando conversas');
      return;
    }

    // Verificar cache primeiro (se não forçar refresh)
    if (!forceRefresh) {
      const cachedConversations = getCachedData('conversations');
      if (cachedConversations) {
        console.log('Usando conversas em cache');
        setConversations(cachedConversations);
        return cachedConversations;
      }
    }

    // Evitar chamadas simultâneas
    if (isLoadingRef.current) {
      console.log('Já carregando conversas, aguardando...');
      return;
    }

    try {
      isLoadingRef.current = true;
      console.log('Carregando conversas do servidor...');

      const data = await fetchConversations();
      
      console.log(`${data?.length || 0} conversas carregadas`);
      setConversations(data || []);
      setCacheData('conversations', data || []);
      
      return data;
    } catch (error) {
      console.error('Erro ao carregar conversas:', error);
      
      if (error.message === 'Unauthorized' && onAuthError) {
        console.log('Erro de autorização detectado ao carregar conversas');
        onAuthError();
        return;
      }

      // Em caso de erro, tentar usar cache
      const cachedConversations = getCachedData('conversations');
      if (cachedConversations) {
        console.log('Usando conversas em cache devido a erro');
        setConversations(cachedConversations);
        return cachedConversations;
      }
      
      return [];
    } finally {
      isLoadingRef.current = false;
    }
  }, [user, fetchConversations, getCachedData, setCacheData, isLoadingRef, onAuthError]);

  // Carregar conversas com debounce
  const debouncedLoadConversations = debouncedFunction(loadConversations, 1000);

  // Criar nova conversa
  const createConversation = useCallback(async (participantIds, title = null) => {
    if (!user || !participantIds || participantIds.length === 0) {
      console.error('Dados inválidos para criar conversa');
      return null;
    }

    try {
      console.log('Criando nova conversa com participantes:', participantIds);
      
      const newConversation = await createConversationAPI(participantIds, title);
      
      if (newConversation) {
        console.log('Nova conversa criada:', newConversation.id);
        
        // Atualizar lista de conversas
        setConversations(prev => [newConversation, ...prev]);
        
        // Emitir evento via WebSocket
        if (socket?.connected) {
          socket.emit('conversation:created', { conversationId: newConversation.id });
        }
        
        return newConversation;
      }
      
      return null;
    } catch (error) {
      console.error('Erro ao criar conversa:', error);
      return null;
    }
  }, [user, createConversationAPI, socket]);

  // Criar ou obter conversa individual
  const createOrGetConversation = useCallback(async (otherUser) => {
    if (!user || !otherUser) {
      console.error('Dados inválidos para criar/obter conversa individual');
      return null;
    }

    // Determinar o ID correto do outro usuário
    let otherUserId;
    if (typeof otherUser === 'string') {
      otherUserId = otherUser;
    } else if (otherUser.id) {
      otherUserId = otherUser.id;
    } else if (otherUser.userId) {
      otherUserId = otherUser.userId;
    } else {
      console.error('Não foi possível determinar o ID do outro usuário:', otherUser);
      return null;
    }

    try {
      console.log('Criando/obtendo conversa individual com:', otherUserId);
      
      const conversation = await createOrGetConversationAPI(otherUserId);
      
      if (conversation) {
        console.log('Conversa individual obtida/criada:', conversation.id);
        
        // Atualizar lista de conversas se for uma nova conversa
        setConversations(prev => {
          const existingConversation = prev.find(c => c.id === conversation.id);
          if (!existingConversation) {
            return [conversation, ...prev];
          }
          return prev;
        });
        
        return conversation;
      }
      
      return null;
    } catch (error) {
      console.error('Erro ao criar/obter conversa individual:', error);
      return null;
    }
  }, [user, createOrGetConversationAPI]);

  // Adicionar participante a grupo
  const addParticipantToGroup = useCallback(async (conversationId, participantId) => {
    if (!conversationId || !participantId) {
      console.error('Dados inválidos para adicionar participante');
      return false;
    }

    try {
      const success = await addParticipantAPI(conversationId, participantId);
      
      if (success) {
        console.log(`Participante ${participantId} adicionado à conversa ${conversationId}`);
        // Recarregar conversas para atualizar participantes
        debouncedLoadConversations(false);
      }
      
      return success;
    } catch (error) {
      console.error('Erro ao adicionar participante:', error);
      return false;
    }
  }, [addParticipantAPI, debouncedLoadConversations]);

  // Adicionar múltiplos participantes
  const addMultipleParticipantsToGroup = useCallback(async (conversationId, participants) => {
    if (!conversationId || !participants || participants.length === 0) {
      console.error('Dados inválidos para adicionar múltiplos participantes');
      return false;
    }

    try {
      const success = await addMultipleParticipantsAPI(conversationId, participants);
      
      if (success) {
        console.log(`${participants.length} participantes adicionados à conversa ${conversationId}`);
        // Recarregar conversas para atualizar participantes
        debouncedLoadConversations(false);
      }
      
      return success;
    } catch (error) {
      console.error('Erro ao adicionar múltiplos participantes:', error);
      return false;
    }
  }, [addMultipleParticipantsAPI, debouncedLoadConversations]);

  // Remover participante de grupo
  const removeParticipantFromGroup = useCallback(async (conversationId, participantId) => {
    if (!conversationId || !participantId) {
      console.error('Dados inválidos para remover participante');
      return false;
    }

    try {
      const success = await removeParticipantAPI(conversationId, participantId);
      
      if (success) {
        console.log(`Participante ${participantId} removido da conversa ${conversationId}`);
        // Recarregar conversas para atualizar participantes
        debouncedLoadConversations(false);
      }
      
      return success;
    } catch (error) {
      console.error('Erro ao remover participante:', error);
      return false;
    }
  }, [removeParticipantAPI, debouncedLoadConversations]);

  // Limpar conversas abandonadas
  const cleanupLeftConversations = useCallback(() => {
    if (!user?.id) return;

    setConversations(prev => {
      return prev.filter(conversation => {
        const userParticipant = conversation.participants?.find(p => 
          p.userId === user.id || p.clientId === user.id
        );
        return userParticipant && !userParticipant.leftAt;
      });
    });
  }, [user?.id]);

  // Atualizar última mensagem de uma conversa
  const updateConversationLastMessage = useCallback((conversationId, message) => {
    if (!conversationId || !message) return;

    setConversations(prev => {
      return prev.map(conversation => {
        if (conversation.id === conversationId) {
          return {
            ...conversation,
            lastMessage: message,
            lastMessageAt: message.createdAt,
            hasUnread: message.senderId !== user?.id && message.senderClientId !== user?.id
          };
        }
        return conversation;
      });
    });
  }, [user?.id]);

  return {
    // State
    conversations,
    activeConversation,
    
    // Actions
    setConversations,
    setActiveConversation,
    loadConversations,
    debouncedLoadConversations,
    createConversation,
    createOrGetConversation,
    addParticipantToGroup,
    addMultipleParticipantsToGroup,
    removeParticipantFromGroup,
    cleanupLeftConversations,
    updateConversationLastMessage
  };
};