"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/users/page",{

/***/ "(app-pages-browser)/./src/contexts/ChatContext.js":
/*!*************************************!*\
  !*** ./src/contexts/ChatContext.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   useChat: () => (/* binding */ useChat),\n/* harmony export */   useChatActions: () => (/* binding */ useChatActions),\n/* harmony export */   useChatData: () => (/* binding */ useChatData),\n/* harmony export */   useChatSocket: () => (/* binding */ useChatSocket),\n/* harmony export */   useChatUI: () => (/* binding */ useChatUI)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_chat_useSocketConnection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hooks/chat/useSocketConnection */ \"(app-pages-browser)/./src/hooks/chat/useSocketConnection.js\");\n/* harmony import */ var _hooks_chat_useCache__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/chat/useCache */ \"(app-pages-browser)/./src/hooks/chat/useCache.js\");\n/* harmony import */ var _hooks_chat_useConversations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/chat/useConversations */ \"(app-pages-browser)/./src/hooks/chat/useConversations.js\");\n/* harmony import */ var _hooks_chat_useMessages__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/chat/useMessages */ \"(app-pages-browser)/./src/hooks/chat/useMessages.js\");\n/* harmony import */ var _hooks_chat_useChatUI__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../hooks/chat/useChatUI */ \"(app-pages-browser)/./src/hooks/chat/useChatUI.js\");\n/* harmony import */ var _hooks_chat_useSocketEvents__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../hooks/chat/useSocketEvents */ \"(app-pages-browser)/./src/hooks/chat/useSocketEvents.js\");\n/* __next_internal_client_entry_do_not_use__ ChatProvider,useChat,useChatData,useChatActions,useChatUI,useChatSocket auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$();\n\n\n// Import custom hooks\n\n\n\n\n\n\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst ChatProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    // Initialize hooks\n    const { socket, isConnected, handleAuthError } = (0,_hooks_chat_useSocketConnection__WEBPACK_IMPORTED_MODULE_3__.useSocketConnection)();\n    const { clearCacheForUser } = (0,_hooks_chat_useCache__WEBPACK_IMPORTED_MODULE_4__.useCache)();\n    const { conversations, activeConversation, setActiveConversation, loadConversations, createConversation, createOrGetConversation, addParticipantToGroup, addMultipleParticipantsToGroup, removeParticipantFromGroup, cleanupLeftConversations, updateConversationLastMessage } = (0,_hooks_chat_useConversations__WEBPACK_IMPORTED_MODULE_5__.useConversations)(socket, handleAuthError);\n    const { messages, unreadCount, loadMessages, sendMessage, loadUnreadCount, markMessagesAsRead, resetUnreadCount, deleteMessages, addMessage, setUnreadCount } = (0,_hooks_chat_useMessages__WEBPACK_IMPORTED_MODULE_6__.useMessages)(socket, handleAuthError);\n    const { isPanelOpen, isModalOpen, isLoading, toggleChatPanel, toggleChatModal, setIsPanelOpen, setIsModalOpen, setIsLoading } = (0,_hooks_chat_useChatUI__WEBPACK_IMPORTED_MODULE_7__.useChatUI)();\n    // Setup socket events\n    const { joinConversationRoom, leaveConversationRoom, emitTyping } = (0,_hooks_chat_useSocketEvents__WEBPACK_IMPORTED_MODULE_8__.useSocketEvents)(socket, isConnected, {\n        addMessage,\n        setUnreadCount: {\n            \"ChatProvider.useSocketEvents\": (count)=>{\n            // Update unread count from socket events\n            }\n        }[\"ChatProvider.useSocketEvents\"],\n        loadUnreadCount,\n        loadConversations,\n        updateConversationLastMessage\n    });\n    // Limpar estado do chat quando o usuário muda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (user) {\n                console.log('Usuário logado, limpando estado do chat para novo usuário:', user.fullName);\n                clearCacheForUser();\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user === null || user === void 0 ? void 0 : user.id,\n        clearCacheForUser\n    ]);\n    // Carregar dados iniciais quando conectado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            if (user && socket && isConnected) {\n                console.log('Socket conectado, carregando dados iniciais do chat...');\n                loadConversations();\n                loadUnreadCount();\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        user,\n        socket,\n        isConnected,\n        loadConversations,\n        loadUnreadCount\n    ]);\n    // Context value\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ChatProvider.useMemo[contextValue]\": ()=>({\n                // Connection state\n                socket,\n                isConnected,\n                // Data\n                conversations,\n                messages,\n                unreadCount,\n                activeConversation,\n                // UI state\n                isPanelOpen,\n                isModalOpen,\n                isLoading,\n                // Conversation actions\n                loadConversations,\n                createConversation,\n                createOrGetConversation,\n                setActiveConversation,\n                addParticipantToGroup,\n                addMultipleParticipantsToGroup,\n                removeParticipantFromGroup,\n                cleanupLeftConversations,\n                // Message actions\n                loadMessages,\n                sendMessage,\n                loadUnreadCount,\n                markMessagesAsRead,\n                resetUnreadCount,\n                deleteMessages,\n                // UI actions\n                toggleChatPanel,\n                toggleChatModal,\n                setIsPanelOpen,\n                setIsModalOpen,\n                setIsLoading,\n                // Socket events\n                joinConversationRoom,\n                leaveConversationRoom,\n                emitTyping\n            })\n    }[\"ChatProvider.useMemo[contextValue]\"], [\n        socket,\n        isConnected,\n        conversations,\n        messages,\n        unreadCount,\n        activeConversation,\n        isPanelOpen,\n        isModalOpen,\n        isLoading,\n        loadConversations,\n        createConversation,\n        createOrGetConversation,\n        setActiveConversation,\n        addParticipantToGroup,\n        addMultipleParticipantsToGroup,\n        removeParticipantFromGroup,\n        cleanupLeftConversations,\n        loadMessages,\n        sendMessage,\n        loadUnreadCount,\n        markMessagesAsRead,\n        resetUnreadCount,\n        deleteMessages,\n        toggleChatPanel,\n        toggleChatModal,\n        setIsPanelOpen,\n        setIsModalOpen,\n        setIsLoading,\n        joinConversationRoom,\n        leaveConversationRoom,\n        emitTyping\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\contexts\\\\ChatContext.js\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatProvider, \"M54hfCvBYmcs7grujOEOJtEXZ7c=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_chat_useSocketConnection__WEBPACK_IMPORTED_MODULE_3__.useSocketConnection,\n        _hooks_chat_useCache__WEBPACK_IMPORTED_MODULE_4__.useCache,\n        _hooks_chat_useConversations__WEBPACK_IMPORTED_MODULE_5__.useConversations,\n        _hooks_chat_useMessages__WEBPACK_IMPORTED_MODULE_6__.useMessages,\n        _hooks_chat_useChatUI__WEBPACK_IMPORTED_MODULE_7__.useChatUI,\n        _hooks_chat_useSocketEvents__WEBPACK_IMPORTED_MODULE_8__.useSocketEvents\n    ];\n});\n_c = ChatProvider;\n// Hook para usar o contexto\nconst useChat = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (!context) {\n        throw new Error('useChat must be used within a ChatProvider');\n    }\n    return context;\n};\n_s1(useChat, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Hook específico para dados do chat (conversas e mensagens)\nconst useChatData = ()=>{\n    _s2();\n    const { conversations, messages, unreadCount, activeConversation } = useChat();\n    return {\n        conversations,\n        messages,\n        unreadCount,\n        activeConversation\n    };\n};\n_s2(useChatData, \"fYQD90NOAxWhiN8ZxKN9gngYOJA=\", false, function() {\n    return [\n        useChat\n    ];\n});\n// Hook específico para ações do chat\nconst useChatActions = ()=>{\n    _s3();\n    const { loadConversations, createConversation, createOrGetConversation, setActiveConversation, addParticipantToGroup, addMultipleParticipantsToGroup, removeParticipantFromGroup, loadMessages, sendMessage, loadUnreadCount, markMessagesAsRead, resetUnreadCount, deleteMessages } = useChat();\n    return {\n        loadConversations,\n        createConversation,\n        createOrGetConversation,\n        setActiveConversation,\n        addParticipantToGroup,\n        addMultipleParticipantsToGroup,\n        removeParticipantFromGroup,\n        loadMessages,\n        sendMessage,\n        loadUnreadCount,\n        markMessagesAsRead,\n        resetUnreadCount,\n        deleteMessages\n    };\n};\n_s3(useChatActions, \"HLXnAyrGp6sNp9v45E3v3w7cSns=\", false, function() {\n    return [\n        useChat\n    ];\n});\n// Hook específico para UI do chat\nconst useChatUI = ()=>{\n    _s4();\n    const { isPanelOpen, isModalOpen, isLoading, toggleChatPanel, toggleChatModal, setIsPanelOpen, setIsModalOpen, setIsLoading } = useChat();\n    return {\n        isPanelOpen,\n        isModalOpen,\n        isLoading,\n        toggleChatPanel,\n        toggleChatModal,\n        setIsPanelOpen,\n        setIsModalOpen,\n        setIsLoading\n    };\n};\n_s4(useChatUI, \"a1GdI6HcQjprunap1HGjmNTLU8c=\", false, function() {\n    return [\n        useChat\n    ];\n});\n// Hook específico para conexão socket\nconst useChatSocket = ()=>{\n    _s5();\n    const { socket, isConnected, joinConversationRoom, leaveConversationRoom, emitTyping } = useChat();\n    return {\n        socket,\n        isConnected,\n        joinConversationRoom,\n        leaveConversationRoom,\n        emitTyping\n    };\n};\n_s5(useChatSocket, \"97j3WZm3iS/mGSui2WVLGombvmo=\", false, function() {\n    return [\n        useChat\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"ChatProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ChatContext.js\n"));

/***/ })

});