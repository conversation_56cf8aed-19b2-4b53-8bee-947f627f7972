# Dockerfile para Next.js 15 com React 19
FROM node:20-alpine AS base

# Instalar dependências
FROM base AS deps
WORKDIR /app

# Copiar arquivos de package para instalar dependências
COPY package.json package-lock.json* ./
RUN npm ci

# Construir o aplicativo
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Accept build argument and set environment variables BEFORE build
ARG NEXT_PUBLIC_API_URL=https://hightide.site/api
ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL

# Build da aplicação
RUN npm run build

# Imagem de produção
FROM base AS runner
WORKDIR /app
ENV NODE_ENV production

# Copiar arquivos necessários
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# Expor porta e configurar variáveis de ambiente
EXPOSE 3000
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"
ENV NEXT_PUBLIC_API_URL=https://hightide.site/api

# Iniciar a aplicação
CMD ["node", "server.js"]
